# Room Search Functionality

## Overview
The search functionality has been enhanced to support both building and room searches. Users can now search for specific rooms (like "RM300") and the system will find the building that contains that room.

## How It Works

### Search Types
1. **Building Search**: Search by building name or description
   - Example: "Engineering", "Business", "CICS"
   
2. **Room Search**: Search by room number or code
   - Example: "RM300", "EMB-101", "LAB-01"

### Search Results
- **Building Results**: Show the building name and description
- **Room Results**: Show the room code and building name with a "Room" badge

### User Experience
1. Click the floating search button (magnifying glass icon)
2. Type in the search query
3. Results show both matching buildings and rooms
4. Click on any result to focus the camera on that building
5. If a room is selected, the building containing that room is highlighted

## Sample Data

### College of Information and Computing Sciences (CICS)
- RM300, RM301, RM302

### College of Engineering (COE) 
- EMB-101, EMB-102, EMB-201, EMB-202, EMB-301, EMB-302
- LAB-01, LAB-02, DEAN-OFFICE

### College of Business Administration and Accountancy (CBAA)
- ACCT-101, ACCT-102, BUS-201, BUS-202, DEAN-301
- CONF-ROOM, LIBRARY

## Testing Examples

Try searching for:
- "RM300" → Should find the room in CICS building
- "EMB" → Should find all EMB rooms in Engineering building  
- "LAB" → Should find laboratory rooms in Engineering building
- "DEAN" → Should find dean offices in multiple buildings
- "Engineering" → Should find the engineering building
- "Computing" → Should find the CICS building

## Technical Implementation

The search functionality is implemented in `src/app/page.tsx`:

1. **SearchResult Type**: Unified type for both building and room results
2. **searchResults useMemo**: Searches through buildings and their rooms
3. **handleSearchSelect**: Handles selection of both building and room results
4. **Enhanced UI**: Shows room badges and descriptive text

The room data is stored in `src/data/campus.ts` in each building's `rooms` array as strings.
