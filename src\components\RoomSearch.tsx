"use client";
import { findRoomById } from "@/data/campus";
import { useState } from "react";

export type RoomSearchProps = {
  onFocus: (world: { x: number; y: number; z: number } | null) => void;
  onSelectBuildingId: (id: string | null) => void;
  // New: optional label setter for focus marker
  onFocusLabel?: (label: string | null) => void;
};

export default function RoomSearch({ onFocus, onSelectBuildingId, onFocusLabel }: RoomSearchProps) {
  const [query, setQuery] = useState("");
  const [error, setError] = useState<string | null>(null);

  function submit() {
    const res = findRoomById(query);
    if (!res) {
      setError("Room not found.");
      onFocus(null);
      onSelectBuildingId(null);
      onFocusLabel?.(null);
      return;
    }
    setError(null);
    onFocus(res.world);
    onSelectBuildingId(res.building.id);
    onFocusLabel?.(`${res.room}${res.room ? ` - ${res.room}` : ""}`);
  }

  return (
    <div className="flex flex-col gap-2 w-full max-w-xl">
      <div className="flex gap-2">
        <input
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && submit()}
          placeholder="Find a room e.g. EMB-07 or RM304"
          className="flex-1 border-2 border-gray-200 rounded-lg px-4 py-3 text-sm outline-none focus:ring-4 focus:ring-msu-maroon/20 focus:border-msu-maroon focus:shadow-lg focus:shadow-msu-maroon/10 transition-all duration-200"
        />
        <button
          onClick={submit}
          className="px-6 py-3 bg-gradient-to-r from-msu-maroon to-msu-maroon-dark text-white rounded-lg hover:shadow-lg hover:scale-105 transition-all duration-200 text-sm font-medium"
        >
          Search
        </button>
      </div>
      {error && (
        <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg px-3 py-2">
          {error}
        </div>
      )}
    </div>
  );
}
