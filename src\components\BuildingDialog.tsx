'use client';
import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { Building, getCollegeByBuilding, getDepartmentByBuilding, getProgramsByCollege } from '@/data/campus';
import { clsx } from 'clsx';

interface BuildingDialogProps {
  building: Building | null;
  open: boolean;
  onClose: () => void;
}

export default function BuildingDialog({ building, open, onClose }: BuildingDialogProps) {
  if (!building) return null;

  const college = getCollegeByBuilding(building.id);
  const department = getDepartmentByBuilding(building.id);
  const programs = college ? getProgramsByCollege(college.id) : [];

  return (
    <Dialog.Root open={open} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-fade-in data-[state=closed]:animate-fade-out" />
        <Dialog.Content className={clsx(
          'fixed left-1/2 top-1/2 z-50 w-full max-w-4xl max-h-[90vh]',
          'transform -translate-x-1/2 -translate-y-1/2',
          'card-modern overflow-hidden',
          'data-[state=open]:animate-scale-in data-[state=closed]:animate-scale-out'
        )}>
          {/* Header */}
          <div className="flex items-start justify-between p-6 border-b border-gray-200">
            <div className="flex-1">
              <Dialog.Title className="text-2xl font-bold text-msu-maroon mb-2">
                {building.name}
              </Dialog.Title>
              {college && (
                <p className="text-lg text-gray-600">{college.name}</p>
              )}
              <div className="flex flex-wrap gap-2 mt-3">
                {building.yearBuilt && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-msu-gold/20 text-msu-maroon">
                    Built {building.yearBuilt}
                  </span>
                )}
                {building.floors && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {building.floors} Floors
                  </span>
                )}
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 capitalize">
                  {building.type} Building
                </span>
              </div>
            </div>
            <Dialog.Close asChild>
              <button className="ml-4 p-2 rounded-full hover:bg-gray-100 transition-colors" aria-label="Close">
                <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </Dialog.Close>
          </div>

          {/* Content */}
          <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
            <div className="p-6 space-y-8">
              {/* Description */}
              {building.description && (
                <section>
                  <h3 className="text-lg font-semibold text-msu-maroon mb-3">About This Building</h3>
                  <p className="text-gray-700 leading-relaxed">{building.description}</p>
                </section>
              )}

              {/* Contact Information */}
              {building.contactInfo && (
                <section>
                  <h3 className="text-lg font-semibold text-msu-maroon mb-3">Contact Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {building.contactInfo.phone && (
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-msu-gold/20 rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-msu-maroon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Phone</p>
                          <p className="text-gray-600">{building.contactInfo.phone}</p>
                        </div>
                      </div>
                    )}
                    {building.contactInfo.email && (
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-msu-gold/20 rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-msu-maroon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Email</p>
                          <p className="text-gray-600">{building.contactInfo.email}</p>
                        </div>
                      </div>
                    )}
                    {building.contactInfo.office && (
                      <div className="flex items-center gap-3 md:col-span-2">
                        <div className="w-10 h-10 bg-msu-gold/20 rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-msu-maroon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Main Office</p>
                          <p className="text-gray-600">{building.contactInfo.office}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </section>
              )}

              {/* Operating Hours */}
              {building.operatingHours && (
                <section>
                  <h3 className="text-lg font-semibold text-msu-maroon mb-3">Operating Hours</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {building.operatingHours.weekdays && (
                        <div>
                          <p className="font-medium text-gray-900">Weekdays</p>
                          <p className="text-gray-600">{building.operatingHours.weekdays}</p>
                        </div>
                      )}
                      {building.operatingHours.weekends && (
                        <div>
                          <p className="font-medium text-gray-900">Weekends</p>
                          <p className="text-gray-600">{building.operatingHours.weekends}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </section>
              )}

              {/* Facilities */}
              {building.facilities && building.facilities.length > 0 && (
                <section>
                  <h3 className="text-lg font-semibold text-msu-maroon mb-3">Facilities & Amenities</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {building.facilities.map((facility, index) => (
                      <div key={index} className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                        <div className="w-2 h-2 bg-msu-gold rounded-full flex-shrink-0" />
                        <span className="text-sm text-gray-700">{facility}</span>
                      </div>
                    ))}
                  </div>
                </section>
              )}

              {/* Departments */}
              {department && (
                <section>
                  <h3 className="text-lg font-semibold text-msu-maroon mb-3">Department</h3>
                  <div className="space-y-3">
                    <div key={department.id} className="p-4 border border-gray-200 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">{department.name}</h4>
                      <p className="text-sm text-gray-600">{department.programs.length} programs offered</p>
                    </div>
                  </div>
                </section>
              )}

              {/* Programs */}
              {programs.length > 0 && (
                <section>
                  <h3 className="text-lg font-semibold text-msu-maroon mb-3">Academic Programs ({programs.length})</h3>
                  <div className="space-y-4">
                    {programs.slice(0, 5).map((program) => (
                      <div key={program.id} className="p-4 border border-gray-200 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">{program.name}</h4>
                        {program.requirements.length > 0 && (
                          <details className="mt-2">
                            <summary className="text-sm text-msu-maroon cursor-pointer hover:text-msu-maroon-dark">
                              View Requirements ({program.requirements.length})
                            </summary>
                            <ul className="mt-2 ml-4 space-y-1">
                              {program.requirements.map((req, index) => (
                                <li key={index} className="text-sm text-gray-600 list-disc">{req}</li>
                              ))}
                            </ul>
                          </details>
                        )}
                      </div>
                    ))}
                    {programs.length > 5 && (
                      <p className="text-sm text-gray-500 text-center">
                        And {programs.length - 5} more programs...
                      </p>
                    )}
                  </div>
                </section>
              )}

              {/* Rooms */}
              {building.rooms.length > 0 && (
                <section>
                  <h3 className="text-lg font-semibold text-msu-maroon mb-3">Rooms ({building.rooms.length})</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {building.rooms.map((room) => (
                      <div key={room} className="p-3 border border-gray-200 rounded-lg">
                        <p className="font-medium text-gray-900">{room}</p>
                      </div>
                    ))}
                  </div>
                </section>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
            <Dialog.Close asChild>
              <button className="px-6 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
                Close
              </button>
            </Dialog.Close>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}