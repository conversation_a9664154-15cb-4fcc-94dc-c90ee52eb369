"use client";
import React, { useEffect, useMemo, useState } from "react";
import dynamic from "next/dynamic";
import * as THREE from "three";
import { campus, Building, findDepartmentById } from "@/data/campus";
import BuildingPanel from "@/components/BuildingPanel";
import Chatbot from "@/components/Chatbot";

const Map3D = dynamic(() => import("@/components/Map3D"), { ssr: false });

type Vec3 = { x: number; y: number; z: number };

type SearchResult = {
  type: 'building' | 'room';
  building: Building;
  room?: string;
  displayName: string;
  description?: string;
};

export default function Page() {
  const [selected, setSelected] = useState<Building | null>(null);
  const [focus, setFocus] = useState<Vec3 | null>(null);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  // New: control Map3D camera zoom (orthographic)
  const [cameraZoom, setCamera<PERSON>oom] = useState<number | undefined>(undefined);
  // Track selected department ID for showing department-specific content
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<string | null>(null);
  useEffect(() => {
    if (selected) {
      setFocus({
        x: selected.position.x,
        y: selected.position.y,
        z: selected.position.z,
      });
    } else {
      setFocus(null);
    }
  }, [selected]);

  const focusVector: THREE.Vector3 | null = useMemo(() => {
    if (!focus) return null;
    return new THREE.Vector3(focus.x, focus.y, focus.z);
  }, [focus]);  // Filter buildings and rooms based on search query
  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) {
      return campus.buildings.map((building): SearchResult => ({
        type: 'building',
        building,
        displayName: building.name,
        description: building.description
      }));
    }

    const query = searchQuery.toLowerCase();
    const results: SearchResult[] = [];

    // Search buildings
    campus.buildings.forEach(building => {
      if (building.name.toLowerCase().includes(query) ||
        (building.description && building.description.toLowerCase().includes(query))) {
        results.push({
          type: 'building',
          building,
          displayName: building.name,
          description: building.description
        });
      }

      // Search rooms within this building
      building.rooms.forEach(room => {
        if (room.toLowerCase().includes(query)) {
          results.push({
            type: 'room',
            building,
            room,
            displayName: `${room} - ${building.name}`,
            description: `Room ${room} in ${building.name}`
          });
        }
      });
    });

    return results;
  }, [searchQuery]);

  const handleSearchSelect = (result: SearchResult) => {
    console.log("Selected search result:", result);
    setSelected(result.building);
    setSelectedDepartmentId(null); // Clear department selection when building is selected from search
    setFocus({
      x: result.building.position.x,
      y: result.building.position.y,
      z: result.building.position.z,
    });
    // Set zoom to 15 when selecting from search
    setCameraZoom(15);
    setSearchOpen(false);
    setSearchQuery("");
  };

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Full Background 3D Map */}
      <div className="absolute inset-0 z-0">
        <Map3D
          onSelectBuilding={(b) => {
            setSelected(b);
            setSelectedDepartmentId(null); // Clear department selection when building is selected directly
          }}
          focusTarget={focusVector}
          selectedBuildingId={selected?.id ?? null}
          selected={selected}
          cameraZoom={cameraZoom}
          setCameraZoom={setCameraZoom}
        />
      </div>

      {/* Floating Header Overlay */}
      <div className="absolute top-0 left-0 right-0 z-30 p-4 md:p-8">
        <div className="max-w-4xl mx-auto hidden">
          <div className="bg-white/90 backdrop-blur-md rounded-2xl p-6 md:p-8 shadow-2xl border border-white/20">
            <div className="text-center">
              <h1 className="text-3xl md:text-5xl font-bold tracking-tight text-msu-maroon mb-3">
                MSU Main Campus
              </h1>
              <p className="text-slate-700 text-base md:text-lg max-w-2xl mx-auto mb-4">
                Explore our beautiful campus in 3D. Find buildings, discover programs, and get instant answers about academics.
              </p>
              <div className="flex items-center gap-2 justify-center">
                <div className="w-2 h-2 bg-msu-gold rounded-full animate-pulse"></div>
                <span className="text-sm text-msu-maroon font-medium">Interactive 3D Experience</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Search Button */}
      <button
        className="fixed top-1/2 left-4 z-40 -translate-y-1/2 rounded-full bg-gradient-to-r from-msu-gold to-yellow-500 text-msu-maroon p-4 shadow-2xl hover:shadow-3xl hover:scale-110 focus:outline-none transition-all duration-300 border-2 border-msu-maroon/20"
        onClick={() => setSearchOpen(true)}
        aria-label="Search buildings and rooms"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </button>

      {/* Search Modal */}
      {searchOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setSearchOpen(false)} />
          <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b bg-gradient-to-r from-msu-maroon to-msu-maroon-dark text-white">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Find Buildings & Rooms</h3>
                <button
                  onClick={() => setSearchOpen(false)}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-4">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search buildings and rooms..."
                className="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-sm outline-none focus:ring-2 focus:ring-msu-maroon/20 focus:border-msu-maroon transition-all duration-200"
                autoFocus
              />
            </div>
            <div className="max-h-64 overflow-y-auto">
              {searchResults.map((result, index) => (
                <button
                  key={`${result.type}-${result.building.id}-${result.room || 'main'}-${index}`}
                  onClick={() => handleSearchSelect(result)}
                  className="w-full text-left p-4 hover:bg-msu-gold/10 border-b border-gray-100 transition-colors"
                >
                  <div className="font-medium text-msu-maroon flex items-center gap-2">
                    {result.type === 'room' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                        Room
                      </span>
                    )}
                    {result.displayName}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">{result.description}</div>
                </button>
              ))}
              {searchResults.length === 0 && searchQuery && (
                <div className="p-4 text-center text-gray-500">
                  No buildings or rooms found matching &ldquo;{searchQuery}&rdquo;
                </div>
              )}
            </div>
          </div>
        </div>
      )}      {/* Building Panel */}
      <BuildingPanel building={selected} selectedDepartmentId={selectedDepartmentId} onClose={() => {
        setSelected(null);
        setSelectedDepartmentId(null);
        setCameraZoom(undefined);
      }}
        onBackToCollege={() => {
          console.log("Back to college - clearing department selection");
          setSelectedDepartmentId(null);
        }}
        onSelectDepartment={(departmentId) => {
          console.log("Department selected:", departmentId);

          // Find the department first
          const departmentData = findDepartmentById(departmentId);
          if (!departmentData) {
            console.warn("Department not found:", departmentId);
            return;
          }

          // Find the building using the department's buildingId
          const departmentBuilding = campus.buildings.find(b => b.id === departmentData.department.buildingId);
          if (!departmentBuilding) {
            console.warn("Building not found for department:", departmentId, "buildingId:", departmentData.department.buildingId);
            return;
          }

          // Check if it's the same building as currently selected
          if (selected && selected.id === departmentData.department.buildingId) {
            // Same building - just update the selected department to show department content
            console.log("Same building - showing department content for:", departmentData.department.name);
            setSelectedDepartmentId(departmentId);
            return;
          }

          // Different building - move focus to new building and set selected department
          console.log("Different building - moving focus to:", departmentBuilding.name, "for department:", departmentData.department.name);
          setSelected(departmentBuilding);
          setSelectedDepartmentId(departmentId);
          setFocus({
            x: departmentBuilding.position.x,
            y: departmentBuilding.position.y,
            z: departmentBuilding.position.z,
          });          // Set zoom to 15 when selecting a department building
          setCameraZoom(15);
        }}
      />

      {/* Enhanced Chatbot */}
      <Chatbot />
    </div>
  );
}
