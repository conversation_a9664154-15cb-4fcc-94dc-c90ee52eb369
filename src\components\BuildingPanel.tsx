'use client';
import { Building, getDepartmentByBuilding, getCollegeByBuilding, isBuildingMainForCollege, getDepartmentsByCollege, findDepartmentById } from "@/data/campus";
import * as Dialog from "@radix-ui/react-dialog";
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";
import { XMarkIcon, ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/solid";
import Image from "next/image";
import React from "react";

export default function BuildingPanel({ building, onClose, onSelectDepartment, selectedDepartmentId, onBackToCollege }: {
  building: Building | null;
  onClose: () => void;
  onSelectDepartment?: (departmentId: string) => void;
  selectedDepartmentId?: string | null;
  onBackToCollege?: () => void;
}) {
  const open = !!building;
    // Get college and department for photos
  const college = building ? getCollegeByBuilding(building.id) : null;

  // If a specific department is selected, use that department; otherwise use the building's default department
  const selectedDepartmentData = selectedDepartmentId ? findDepartmentById(selectedDepartmentId) : null;
  const department = selectedDepartmentData ? selectedDepartmentData.department : (building ? getDepartmentByBuilding(building.id) : null);
  
  // Decide photo source based on the current dialog content
  // - Department view: use department photos
  // - College view: use college photos
  const isMain = building ? isBuildingMainForCollege(building.id) : false;
  const isDepartmentView = !!(selectedDepartmentId || !isMain);

  const photos: string[] = isDepartmentView
    ? (department?.photos ?? [])
    : (college?.photos ?? []);

  const photoCredits: string[] = isDepartmentView
    ? (department?.photoCredits ?? [])
    : (college?.photoCredits ?? []);
  
  const [index, setIndex] = React.useState(0);
  const [enlargedPhotoOpen, setEnlargedPhotoOpen] = React.useState(false);

  // Mobile bottom-sheet state
  const [isMobile, setIsMobile] = React.useState(false);
  const MIN_H = 240; // collapsed height
  const [maxH, setMaxH] = React.useState(560);
  const [sheetH, setSheetH] = React.useState(MIN_H);
  const dragRef = React.useRef<{ startY: number; startH: number; dragging: boolean }>({ startY: 0, startH: MIN_H, dragging: false });
  React.useEffect(() => {
    const mq = window.matchMedia("(max-width: 639px)");
    const apply = () => setIsMobile(mq.matches);
    apply();
    mq.addEventListener("change", apply);
    const onResize = () => setMaxH(window.innerHeight);
    onResize();
    window.addEventListener("resize", onResize);
    return () => {
      mq.removeEventListener("change", apply);
      window.removeEventListener("resize", onResize);
    };
  }, []);  // Reset carousel index and sheet height when building changes or opens
  React.useEffect(() => {
    setIndex(0);
    setSheetH(MIN_H);
  }, [building?.id]);

  const next = React.useCallback(() => {
    if (!photos.length) return;
    setIndex((i) => (i + 1) % photos.length);
  }, [photos.length]);
  const prev = React.useCallback(() => {
    if (!photos.length) return;
    setIndex((i) => (i - 1 + photos.length) % photos.length);
  }, [photos.length]);

  // Keyboard navigation for enlarged photo modal
  React.useEffect(() => {
    if (!enlargedPhotoOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        prev();
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        next();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        setEnlargedPhotoOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [enlargedPhotoOpen, prev, next]);
  const onHandlePointerDown = (e: React.PointerEvent) => {
    if (!isMobile) return;
    e.preventDefault();
    const element = e.currentTarget as HTMLElement;
    element.setPointerCapture(e.pointerId);
    dragRef.current = { startY: e.clientY, startH: sheetH, dragging: true };
  };

  const onHandlePointerMove = (e: React.PointerEvent) => {
    if (!isMobile || !dragRef.current.dragging) return;
    e.preventDefault();
    const dy = dragRef.current.startY - e.clientY; // up = increase height
    const nextH = Math.max(MIN_H, Math.min(maxH, dragRef.current.startH + dy));
    setSheetH(nextH);
  };

  const onHandlePointerUp = (e: React.PointerEvent) => {
    if (!isMobile) return;
    e.preventDefault();
    const element = e.currentTarget as HTMLElement;
    element.releasePointerCapture(e.pointerId);

    if (!dragRef.current.dragging) return;
    const totalDy = dragRef.current.startY - e.clientY;
    dragRef.current.dragging = false;

    // Snap behavior
    const mid = (MIN_H + maxH) / 2;
    if (sheetH >= mid) {
      setSheetH(maxH);
    } else if (sheetH <= MIN_H + 40) {
      // If user dragged down past threshold while near collapsed state, close
      if (totalDy < -80) onClose();
      else setSheetH(MIN_H);
    } else {
      setSheetH(MIN_H);
    }
  };  const isMainBuilding = building ? isBuildingMainForCollege(building.id) : false;
  const departments = college ? getDepartmentsByCollege(college.id) : [];
  const programs = department ? department.programs : [];

  // Determine if we should show department content (either selected department or non-main building)
  const showDepartmentContent = selectedDepartmentId || !isMainBuilding;
  
  return (
    <Dialog.Root open={open} onOpenChange={(isOpen) => { if (!isOpen) onClose(); }}>
      <Dialog.Portal>
        {building ? (
          <Dialog.Content asChild>
            <div
              className={
                "fixed z-40 overflow-hidden bg-white/95 backdrop-blur-xl border border-slate-200 shadow-2xl flex flex-col " +
                // Mobile: bottom sheet full-width
                "inset-x-0 bottom-0 rounded-t-2xl sm:inset-auto sm:bottom-4 sm:right-4 sm:w-[90vw] sm:max-w-[420px] sm:rounded-xl"
              }
              style={isMobile ? { height: sheetH } : undefined}
            >              {/* Drag handle for mobile */}
              <div
                className="sm:hidden flex items-center justify-center py-3 cursor-grab active:cursor-grabbing touch-none"
                onPointerDown={onHandlePointerDown}
                onPointerMove={onHandlePointerMove}
                onPointerUp={onHandlePointerUp}
                style={{ touchAction: 'none' }}
              >
                <div className="h-1.5 w-12 rounded-full bg-slate-300" />
              </div>

              {/* Header / Image */}
              <div className="relative">                {/* Image area with simple carousel */}
                <div
                  className="aspect-video bg-slate-100 overflow-hidden relative cursor-pointer"
                  onClick={() => setEnlargedPhotoOpen(true)}
                >
                  {photos.length > 0 ? (
                    <>                      <Image
                      src={photos[index]}
                      alt={building.name}
                      fill
                      className="object-cover"
                      draggable={false}
                      priority={index === 0}
                    />
                      {/* Photo credit overlay */}
                      {photoCredits[index] && (
                        <div className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                          {photoCredits[index]}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="flex h-full items-center justify-center text-slate-400 text-sm">No image available</div>
                  )}
                </div>

                {/* Carousel controls */}
                {photos.length > 1 && (
                  <>
                    <button
                      onClick={prev}
                      className="absolute left-2 top-1/2 -translate-y-1/2 rounded-full bg-black/40 text-white p-2 hover:bg-black/60 focus:outline-none"
                      aria-label="Previous photo"
                    >
                      ‹
                    </button>
                    <button
                      onClick={next}
                      className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full bg-black/40 text-white p-2 hover:bg-black/60 focus:outline-none"
                      aria-label="Next photo"
                    >
                      ›
                    </button>
                    <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
                      {photos.map((_, i) => (
                        <span key={i} className={`h-1.5 w-1.5 rounded-full ${i === index ? "bg-white" : "bg-white/50"}`}></span>
                      ))}
                    </div>
                  </>
                )}

                {/* Close button */}
                <Dialog.Close asChild>
                  <button
                    className="absolute top-2 right-2 rounded-full bg-black/40 text-white p-2 hover:bg-black/60 focus:outline-none"
                    aria-label="Close"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </Dialog.Close>
              </div>

              {/* Title / Quick meta */}
              <div className="px-4 pt-3 pb-2 border-b border-slate-200">
                <div className="flex items-start justify-between gap-3">
                  <div className="flex items-start gap-2 flex-1">
                    {/* Back button - only show when viewing department content in a main building */}
                    {showDepartmentContent && isMainBuilding && selectedDepartmentId && (
                      <button
                        onClick={() => onBackToCollege?.()}
                        className="flex-shrink-0 p-1 hover:bg-slate-100 rounded-md transition-colors mt-0.5"
                        title="Back to college overview"
                      >
                        <ChevronLeftIcon className="w-4 h-4 text-slate-600" />
                      </button>
                    )}
                    <div>
                      <Dialog.Title className="font-semibold text-slate-900 leading-tight">{showDepartmentContent && department ? department.name : (college && isMainBuilding) ? college.name : building.name}</Dialog.Title>
                      {college && (
                        <div className="text-xs text-slate-600 mt-1">
                          {building.name}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>              {/* Scrollable content */}
              <div className="flex-1 overflow-y-auto max-h-[53vh] p-4 space-y-6">
                {/* Description */}
                {building.description && (
                  <section>
                    <p className="text-sm text-slate-700 leading-relaxed">{building.description}</p>
                  </section>
                )}

                {/* Main Building Content */}
                {isMainBuilding && !showDepartmentContent ? (
                  <>
                    {/* Clickable Department Buildings */}
                    {departments.length > 0 && (
                      <section>
                        <h3 className="text-xs font-medium text-slate-600 mb-2">Departments</h3>
                        <div className="space-y-2">
                          {departments.map((deptBuilding) => (
                            <button
                              key={deptBuilding.id}
                              onClick={() => onSelectDepartment?.(deptBuilding.id)}
                              className="w-full p-3 rounded-lg border border-slate-200 bg-slate-50 hover:bg-slate-100 transition-colors text-left"
                            >
                              <div className="font-medium text-sm text-slate-900">{deptBuilding.name}</div>
                            </button>
                          ))}
                        </div>
                      </section>
                    )}

                    {/* Contact Information for Main Buildings */}
                    {building.contactInfo && (
                      <section>
                        <h3 className="text-xs font-medium text-slate-600 mb-2">Contact Information</h3>
                        <div className="space-y-2 text-sm">
                          {building.contactInfo.phone && (
                            <div className="flex items-center gap-2">
                              <span className="text-slate-600">Phone:</span>
                              <span className="text-slate-900">{building.contactInfo.phone}</span>
                            </div>
                          )}
                          {building.contactInfo.email && (
                            <div className="flex items-center gap-2">
                              <span className="text-slate-600">Email:</span>
                              <span className="text-slate-900">{building.contactInfo.email}</span>
                            </div>
                          )}
                          {building.contactInfo.office && (
                            <div className="flex items-center gap-2">
                              <span className="text-slate-600">Office:</span>
                              <span className="text-slate-900">{building.contactInfo.office}</span>
                            </div>
                          )}
                        </div>
                      </section>
                    )}
                  </>
                ) : (
                  /* Additional Building Content */
                  <>
                    {/* Programs in Accordion Format */}
                    {programs.length > 0 && (
                      <section>
                        <h3 className="text-xs font-medium text-slate-600 mb-3">Offered Programs ({programs.length})</h3>
                        <div className="space-y-3">
                          {programs.map((program) => (
                            <details key={program.id} className="group border border-slate-200 rounded-lg">
                              <summary className="cursor-pointer p-3 hover:bg-slate-50 transition-colors">
                                <div className="font-medium text-sm text-slate-900 inline">{program.name}</div>
                                {program.description && (
                                  <div className="text-xs text-slate-500 mt-1">{program.description}</div>
                                )}
                              </summary>
                              <div className="border-t border-slate-200 p-3 bg-slate-50">
                                {/* Program Requirements */}
                                {program.requirements.length > 0 && (
                                  <div className="mb-3">
                                    <h4 className="text-xs font-medium text-slate-600 mb-2">Program Requirements</h4>
                                    <ul className="space-y-1">
                                      {program.requirements.map((req, i) => (
                                        <li key={i} className="text-xs text-slate-600 flex items-start gap-2">
                                          <span className="w-1 h-1 bg-slate-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                          <span>{req}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}

                                {/* Department-level Requirements */}
                                {(() => {
                                  const department = departments.find(dept => dept.programs.some(p => p.id === program.id));
                                  return department?.requirements && department.requirements.length > 0 ? (
                                    <div>
                                      <h4 className="text-xs font-medium text-slate-600 mb-2">Department Requirements</h4>
                                      <ul className="space-y-1">
                                        {department.requirements.map((req, i) => (
                                          <li key={i} className="text-xs text-slate-600 flex items-start gap-2">
                                            <span className="w-1 h-1 bg-slate-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                            <span>{req}</span>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  ) : null;
                                })()}
                              </div>
                            </details>
                          ))}
                        </div>
                      </section>
                    )}
                  </>
                )}

                {/* Rooms (for both building types) */}
                {building.rooms.length > 0 && (
                    <section>
                    <h3 className="text-xs font-medium text-slate-600 mb-3">Rooms ({building.rooms.length})</h3>
                    <details className="group border border-slate-200 rounded-lg">
                      <summary className="cursor-pointer p-3 hover:bg-slate-50 transition-colors">
                        
                      </summary>
                      <div className="border-t border-slate-200 p-3">
                      <div className="grid grid-cols-2 gap-2">
                        {building.rooms.map((room) => (
                        <div key={room} className="rounded border border-slate-200 px-2 py-1 text-sm">
                          <div className="font-medium text-slate-900">{room}</div>
                          {room && <div className="text-xs text-slate-600">{room}</div>}
                        </div>
                        ))}
                      </div>
                      </div>
                    </details>
                    </section>
                )}

                {/* Empty state */}
                {departments.length === 0 && programs.length === 0 && building.rooms.length === 0 && !building.description && departments.length === 0 && (
                  <div className="text-sm text-slate-500 text-center py-8">No information available for this building.</div>
                )}</div>
            </div>
          </Dialog.Content>
        ) : null}
      </Dialog.Portal>

      {/* Enlarged Photo Modal */}
      <Dialog.Root open={enlargedPhotoOpen} onOpenChange={setEnlargedPhotoOpen}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/80 z-50" />          <Dialog.Content className="fixed inset-0 z-50 flex items-center justify-center p-4">
            <VisuallyHidden.Root>
              <Dialog.Title>
                {building?.name} - Photo Gallery
              </Dialog.Title>
            </VisuallyHidden.Root>
            <div className="relative max-w-7xl max-h-full w-full h-full flex items-center justify-center">
              {/* Close button */}
              <Dialog.Close asChild>
                <button
                  className="absolute top-4 right-4 z-10 rounded-full bg-black/40 text-white p-3 hover:bg-black/60 focus:outline-none"
                  aria-label="Close enlarged photo"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </Dialog.Close>

              {/* Navigation buttons */}
              {photos.length > 1 && (
                <>
                  <button
                    onClick={prev}
                    className="absolute left-4 top-1/2 -translate-y-1/2 z-10 rounded-full bg-black/40 text-white p-3 hover:bg-black/60 focus:outline-none"
                    aria-label="Previous photo"
                  >
                    <ChevronLeftIcon className="h-8 w-8" />
                  </button>
                  <button
                    onClick={next}
                    className="absolute right-4 top-1/2 -translate-y-1/2 z-10 rounded-full bg-black/40 text-white p-3 hover:bg-black/60 focus:outline-none"
                    aria-label="Next photo"
                  >
                    <ChevronRightIcon className="h-8 w-8" />
                  </button>
                </>
              )}              
              {/* Photo */}
              {photos.length > 0 && (
                <div className="relative max-w-full max-h-full flex items-center justify-center">
                  <Image
                    src={photos[index]}
                    alt={`${building?.name} - Photo ${index + 1}`}
                    width={1200}
                    height={800}
                    className="max-w-full max-h-full object-contain"
                    draggable={false}
                    priority
                  />

                  {/* Photo credit */}
                  {photoCredits[index] && (
                    <div className="absolute bottom-4 right-4 bg-black/60 text-white text-sm px-3 py-2 rounded">
                      {photoCredits[index]}
                    </div>
                  )}

                  {/* Photo counter */}
                  {photos.length > 1 && (
                    <div className="absolute bottom-4 left-4 bg-black/60 text-white text-sm px-3 py-2 rounded">
                      {index + 1} / {photos.length}
                    </div>
                  )}
                </div>
              )}

              {/* Thumbnail navigation for multiple photos */}
              {photos.length > 1 && (
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 max-w-full overflow-x-auto px-4">
                  {photos.map((photo, i) => (<button
                    key={i}
                    onClick={() => setIndex(i)}
                    className={`flex-shrink-0 w-16 h-12 rounded border-2 overflow-hidden relative ${i === index ? "border-white" : "border-white/30"
                      }`}
                  >
                    <Image
                      src={photo}
                      alt={`Thumbnail ${i + 1}`}
                      fill
                      className="object-cover"
                    />
                  </button>
                  ))}
                </div>
              )}
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </Dialog.Root>
  );
}