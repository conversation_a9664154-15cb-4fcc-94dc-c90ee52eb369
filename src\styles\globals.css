@import "tailwindcss";

:root {
  --msu-maroon: #6f1926;
  --msu-gold: #f5db2e;
  --msu-maroon-dark: #5a1420;
  --msu-maroon-light: #8b2332;
  --msu-gold-dark: #d4c026;
  --msu-gold-light: #f7e555;
  
  /* Semantic colors */
  --primary: var(--msu-maroon);
  --primary-foreground: white;
  --secondary: var(--msu-gold);
  --secondary-foreground: var(--msu-maroon);
  --accent: var(--msu-gold-light);
  --accent-foreground: var(--msu-maroon-dark);
  
  /* Background gradients */
  --gradient-primary: linear-gradient(135deg, var(--msu-maroon) 0%, var(--msu-maroon-dark) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--msu-gold) 0%, var(--msu-gold-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--msu-gold-light) 0%, var(--msu-gold) 100%);
}

/* Custom scrollbar for modern look */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--msu-maroon);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--msu-maroon-dark);
}

/* Glass morphism utility classes */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* MSU themed button styles */
.btn-msu-primary {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  box-shadow: 0 4px 6px -1px rgba(111, 25, 38, 0.3);
  transition: all 0.2s ease;
}

.btn-msu-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 15px -3px rgba(111, 25, 38, 0.4);
}

.btn-msu-secondary {
  background: var(--gradient-secondary);
  color: var(--msu-maroon);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  box-shadow: 0 4px 6px -1px rgba(245, 219, 46, 0.3);
  transition: all 0.2s ease;
}

.btn-msu-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 15px -3px rgba(245, 219, 46, 0.4);
}

/* Floating action button base styles */
.fab {
  position: fixed;
  z-index: 50;
  border-radius: 50%;
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px -8px rgba(0, 0, 0, 0.4);
}

/* Modern card styles */
.card-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Full screen layout */
.full-screen {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Responsive text utilities */
.text-responsive-xl {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

.text-responsive-lg {
  font-size: clamp(1.25rem, 3vw, 1.875rem);
}

.text-responsive-base {
  font-size: clamp(0.875rem, 2vw, 1rem);
}
