# MSU-Main Campus Interactive 3D Map

This project is an interactive 3D map of the Mindanao State University - Main Campus. It allows users to explore the campus in a 3D environment, view information about buildings, search for rooms, and get answers to academic questions through a chatbot.

## Technologies Used

*   **Next.js**: A React framework for building server-side rendered and statically generated web applications.
*   **React**: A JavaScript library for building user interfaces.
*   **Three.js**: A 3D graphics library for creating and displaying animated 3D computer graphics in a web browser.
*   **@react-three/fiber**: A React renderer for Three.js.
*   **@react-three/drei**: A collection of useful helpers and abstractions for @react-three/fiber.
*   **Tailwind CSS**: A utility-first CSS framework for rapidly building custom user interfaces.
*   **TypeScript**: A typed superset of JavaScript that compiles to plain JavaScript.

## Features

*   **Interactive 3D Campus Map**: Explore a 3D model of the MSU-Main Campus.
*   **Building Information**: Click on a building to view its name, college, and other information.
*   **Room Search**: Search for a specific room and see its location on the map.
*   **Academic Q&A Chatbot**: Ask questions about academic programs, departments, and requirements.

## Getting Started

First, install the dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Codebase Structure

*   **`src/app/page.tsx`**: The main page of the application, which integrates all the other components.
*   **`src/components/Map3D.tsx`**: The 3D map component, which uses Three.js to render the campus model.
*   **`src/components/BuildingSidebar.tsx`**: The sidebar component that displays information about the selected building.
*   **`src/components/RoomSearch.tsx`**: The room search component.
*   **`src/components/Chatbot.tsx`**: The chatbot component.
*   **`src/data/campus.ts`**: The data source for the application, which contains information about the campus, colleges, buildings, departments, and programs.
*   **`src/app/api/chat/route.ts`**: The chatbot API, which provides answers to user questions based on the campus data.
*   **`public/scene.glb`**: The 3D model of the campus.