'use client';
import React, { useEffect, useState } from 'react';
import * as Popover from '@radix-ui/react-popover';
import { Building, getCollegeByBuilding, getDepartmentByBuilding } from '@/data/campus';
import { clsx } from 'clsx';

interface BuildingPopoverProps {
  building: Building | null;
  onClose: () => void;
  onViewDetails: () => void;
}

export default function BuildingPopover({ building, onClose, onViewDetails }: BuildingPopoverProps) {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(!!building);
  }, [building]);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
    setIsOpen(open);
  };

  if (!building) return null;

  const college = getCollegeByBuilding(building.id);
  const department = getDepartmentByBuilding(building.id);

  return (
    <Popover.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Popover.Anchor asChild>
        <div 
          className="fixed pointer-events-none z-30"
          style={{
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        />
      </Popover.Anchor>
      
      <Popover.Portal>
        <Popover.Content
          className={clsx(
            'z-50 w-80 max-w-[90vw]',
            'card-modern p-6',
            'animate-scale-in',
            'data-[state=open]:animate-fade-in',
            'data-[state=closed]:animate-fade-out'
          )}
          sideOffset={5}
          align="center"
        >
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="font-bold text-lg text-msu-maroon leading-tight">
                {building.name}
              </h3>
              {college && (
                <p className="text-sm text-gray-600 mt-1">
                  {college.name}
                </p>
              )}
            </div>
            <button
              onClick={onClose}
              className="ml-2 p-1 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Close"
            >
              <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Quick Info */}
          <div className="space-y-3 mb-5">
            {building.description && (
              <p className="text-sm text-gray-700 leading-relaxed">
                {building.description.length > 120 
                  ? `${building.description.substring(0, 120)}...` 
                  : building.description
                }
              </p>
            )}

            <div className="flex flex-wrap gap-2">
              {building.yearBuilt && (
                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-msu-gold/20 text-msu-maroon">
                  Built {building.yearBuilt}
                </span>
              )}
              {building.floors && (
                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {building.floors} Floors
                </span>
              )}
              {department && (
                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  1 Department
                </span>
              )}
            </div>

            {/* Operating Hours */}
            {building.operatingHours && (
              <div className="text-xs text-gray-600">
                <div className="flex items-center gap-1">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Weekdays: {building.operatingHours.weekdays}</span>
                </div>
                {building.operatingHours.weekends && (
                  <div className="flex items-center gap-1 mt-1">
                    <svg className="w-3 h-3 opacity-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Weekends: {building.operatingHours.weekends}</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={onViewDetails}
              className="flex-1 btn-msu-primary text-sm py-2.5 px-4 rounded-lg font-medium transition-all duration-200"
            >
              View More Details
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2.5 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              Close
            </button>
          </div>

          <Popover.Arrow className="fill-white drop-shadow-sm" />
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
}