import "../styles/globals.css";
import React from "react";

export const metadata = {
  title: "MSU - Main Campus Guide",
  description: "Interactive 3D campus map with modern UI, room finder, and academic chatbot.",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="antialiased bg-gradient-to-br from-slate-50 to-slate-100 text-slate-900 overflow-hidden">
        <div id="portal-root" />
        <main className="full-screen">{children}</main>
      </body>
    </html>
  );
}
