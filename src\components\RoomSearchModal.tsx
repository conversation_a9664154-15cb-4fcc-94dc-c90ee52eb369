'use client';
import React, { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { findRoomById } from '@/data/campus';
import { clsx } from 'clsx';

interface RoomSearchModalProps {
  open: boolean;
  onClose: () => void;
  onFocus: (world: { x: number; y: number; z: number } | null) => void;
  onSelectBuildingId: (id: string | null) => void;
  onFocusLabel?: (label: string | null) => void;
}

export default function RoomSearchModal({ 
  open, 
  onClose, 
  onFocus, 
  onSelectBuildingId, 
  onFocusLabel 
}: RoomSearchModalProps) {
  const [query, setQuery] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('msu-recent-searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch {
        // Ignore parsing errors
      }
    }
  }, []);

  const saveRecentSearch = (searchQuery: string) => {
    const updated = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('msu-recent-searches', JSON.stringify(updated));
  };

  const handleSearch = async () => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) return;

    setIsSearching(true);
    setError(null);

    // Simulate search delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));

    const result = findRoomById(trimmedQuery);
    
    if (!result) {
      setError("Room not found. Try searching for room codes like 'EMB-07' or 'RM304'.");
      onFocus(null);
      onSelectBuildingId(null);
      onFocusLabel?.(null);
    } else {
      setError(null);
      onFocus(result.world);
      onSelectBuildingId(result.building.id);
      onFocusLabel?.(result.room);
      saveRecentSearch(trimmedQuery);
      onClose();
    }

    setIsSearching(false);
  };

  const handleRecentSearchClick = (searchQuery: string) => {
    setQuery(searchQuery);
    handleSearch();
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('msu-recent-searches');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Sample room suggestions
  const sampleRooms = ['EMB-07', 'RM304', 'LAB-101', 'OFFICE-201', 'HALL-A'];

  return (
    <Dialog.Root open={open} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-fade-in data-[state=closed]:animate-fade-out" />
        <Dialog.Content className={clsx(
          'fixed left-1/2 top-1/2 z-50 w-full max-w-md',
          'transform -translate-x-1/2 -translate-y-1/2',
          'card-modern p-6',
          'data-[state=open]:animate-scale-in data-[state=closed]:animate-scale-out'
        )}>
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-xl font-bold text-msu-maroon">
              Find a Room
            </Dialog.Title>
            <Dialog.Close asChild>
              <button className="p-2 rounded-full hover:bg-gray-100 transition-colors" aria-label="Close">
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </Dialog.Close>
          </div>

          {/* Search Input */}
          <div className="space-y-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Enter room code (e.g., EMB-07, RM304)"
                className={clsx(
                  'w-full pl-10 pr-4 py-3 border rounded-lg',
                  'focus:ring-2 focus:ring-msu-maroon/20 focus:border-msu-maroon',
                  'transition-colors duration-200',
                  error ? 'border-red-300' : 'border-gray-300'
                )}
                autoFocus
              />
            </div>

            {/* Error Message */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            {/* Search Button */}
            <button
              onClick={handleSearch}
              disabled={isSearching || !query.trim()}
              className={clsx(
                'w-full btn-msu-primary py-3 rounded-lg font-medium',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'flex items-center justify-center gap-2'
              )}
            >
              {isSearching ? (
                <>
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Searching...
                </>
              ) : (
                'Search Room'
              )}
            </button>
          </div>

          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-700">Recent Searches</h3>
                <button
                  onClick={clearRecentSearches}
                  className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Clear
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleRecentSearchClick(search)}
                    className="px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                  >
                    {search}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Sample Searches */}
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Try These Examples</h3>
            <div className="flex flex-wrap gap-2">
              {sampleRooms.map((room, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setQuery(room);
                    setTimeout(handleSearch, 100);
                  }}
                  className="px-3 py-1.5 text-sm bg-msu-gold/20 hover:bg-msu-gold/30 text-msu-maroon rounded-full transition-colors"
                >
                  {room}
                </button>
              ))}
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              <strong>Tip:</strong> Room codes typically follow formats like &quot;EMB-07&quot; (building-room) or &quot;RM304&quot; (room number).
            </p>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}