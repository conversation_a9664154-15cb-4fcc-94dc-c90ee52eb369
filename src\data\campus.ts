export type Program = {
  id: string;
  name: string;
  description?: string; // program description for accordion display
  requirements: string[]; // enrollment/shifting requirements
};

export type Department = {
  id: string;
  name: string;
  buildingId: string; // which building this department is located in
  requirements?: string[]; // department-level requirements
  programs: Program[];
  // Department photos for gallery/carousel usage
  photos?: string[];
  // Photo credits for attribution
  photoCredits?: string[];
};

export type Building = {
  id: string; // matches 3D mesh material name (e.g., "coe", "cnsm-building-1")
  name: string;
  acronym: string; // which college this building belongs to
  type: "main" | "additional"; // main building or additional building
  // Center position in world coordinates (leave blank to fill manually)
  position: { x: number; y: number; z: number };
  // Box size in world units (width, height, depth)
  size: { w: number; h: number; d: number };
  color?: string;
  rooms: string[]; // Enhanced building information
  description?: string;
  yearBuilt?: number;
  floors?: number;
  contactInfo?: {
    phone?: string;
    email?: string;
    office?: string;
  };
  operatingHours?: {
    weekdays?: string;
    weekends?: string;
  };
  facilities?: string[];
};

export type College = {
  id: string; // short identifier (e.g., "coe", "cnsm", "cssh")
  name: string;
  mainBuildingId: string; // reference to the main building
  buildingIds: string[]; // all buildings belonging to this college (including main)
  departments: Department[];
  // College photos for gallery/carousel usage
  photos?: string[];
  // Photo credits for attribution
  photoCredits?: string[];
};

export type CampusData = {
  name: string;
  boundary: { minX: number; maxX: number; minZ: number; maxZ: number };
  colleges: College[];
  buildings: Building[];
};

// NOTE: Sample data based on programs_by_college.json. Positions and sizes to be filled manually.
export const campus: CampusData = {
  name: "MSU - Main Campus",
  boundary: { minX: -50, maxX: 215, minZ: -100, maxZ: 100 },
  colleges: [    {
      id: "coa",
      name: "College of Agriculture",
      mainBuildingId: "coa007",
      buildingIds: ["coa007", "coa008", "coa005"],
      photos: [
        "data:image/jpeg;base64,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",
      ],
      photoCredits: ["Photo by Unsplash"],
      departments: [
        {
          id: "coa-agribusiness",
          name: "Agribusiness Management Department",
          buildingId: "coa007",
          requirements: [
            "High School Diploma or equivalent",
            "Strong interest in agricultural business",
            "Basic mathematical skills",
            "Good communication skills",
          ],
          programs: [
            {
              id: "coa-bsam",
              name: "Bachelor of Science in Agribusiness Management",
              description:
                "This program focuses on the business aspects of agriculture, including marketing, finance, and management principles applied to agricultural enterprises. Students learn to bridge the gap between agricultural production and business operations.",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsafp",
              name: "Bachelor of Science in Agriculture Major in Food Processing",
              description:
                "This program combines agricultural knowledge with food science and technology. Students learn modern food processing techniques, quality control, and food safety management to create value-added agricultural products.",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-damfp",
              name: "Diploma in Agribusiness Management Major in Food Processing",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
              ],
            },
          ],
          photos: [
            "data:image/jpeg;base64,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",
          ],
          photoCredits: [
            "Photo by Unsplash - Bus",
          ],

        },
        {
          id: "coa-agext",
          name: "Agricultural Education & Extension Department",
          buildingId: "coa007",
          programs: [
            {
              id: "coa-bsaae",
              name: "Bachelor of Science in Agriculture, Major in Agricultural Extension",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
          photos: [
            "data:image/jpeg;base64,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",
          ],
          photoCredits: [
            "Photo by Unsplash - edu",
          ],
        },
        {
          id: "coa-animalscience",
          name: "Animal Science Department",
          buildingId: "coa008",
          programs: [
            {
              id: "coa-bsaas",
              name: "Bachelor of Science in Agriculture major in Animal Science",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-msas",
              name: "Master of Science in Animal Science",
              requirements: [
                "Bachelor's degree in related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
          ],
          photos: [
            "https://images.unsplash.com/photo-1504307651254-35680f356dfd?q=80&w=1200&auto=format&fit=crop",
            "https://images.unsplash.com/photo-1581094794329-c8112a89af12?q=80&w=1200&auto=format&fit=crop",
          ],
          photoCredits: [
            "Photo by Unsplash - Animal Science",
            "Photo by Unsplash - Farm",
          ],

        },
        {
          id: "coa-plantscience",
          name: "Department of Plant Science",
          buildingId: "coa005",
          programs: [
            {
              id: "coa-bsa-agronomy",
              name: "BSA - Agronomy",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsa-horticulture",
              name: "BSA - Horticulture",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsa-farming",
              name: "BSA - Farming Systems",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsa-soil",
              name: "BSA - Soil Science",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
      ],
    },    {
      id: "cbaa",
      name: "College of Business Administration and Accountancy",
      mainBuildingId: "cbba003",
      buildingIds: ["cbba003"],
      photos: [
        "https://images.unsplash.com/photo-*************-1fb2b075b655?q=80&w=1200&auto=format&fit=crop",
        "https://images.unsplash.com/photo-*************-9d179a990aa6?q=80&w=1200&auto=format&fit=crop",
      ],
      photoCredits: ["Photo by Unsplash", "Photo by Unsplash"],
      departments: [
        {
          id: "cbaa-accountancy",
          name: "Accountancy Department",
          buildingId: "cbba003",
          programs: [
            {
              id: "cbaa-bsa",
              name: "Bachelor of Science in Accountancy",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
                "Mathematics Proficiency Test",
              ],
            },
          ],
        },
        {
          id: "cbaa-economics",
          name: "Economics Department",
          buildingId: "cbba003",
          programs: [
            {
              id: "cbaa-bsbae",
              name: "Bachelor of Science in Business Administration, major in Economics",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
        {
          id: "cbaa-mba",
          name: "Masters of Business Administration",
          buildingId: "cbba003",
          programs: [
            {
              id: "cbaa-mba-ibf",
              name: "Master of Business Administration - Islamic Banking and Finance",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-fm",
              name: "Master of Business Administration - Financial Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-hrm",
              name: "Master of Business Administration - Human Resource Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-mm",
              name: "Master of Business Administration - Marketing Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-olm",
              name: "Master of Business Administration - Organizational Leadership and Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-sed",
              name: "Master of Business Administration - Sustainable Enterprise Development",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
          ],
        },
        {
          id: "cbaa-management",
          name: "Management Department",
          buildingId: "cbba003",
          programs: [
            {
              id: "cbaa-bsbam",
              name: "Bachelor of Science in Business Administration, major in Management",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "cbaa-bsbahrm",
              name: "Bachelor of Science in Business Administration, major in Human Resource Management",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
        {
          id: "cbaa-marketing",
          name: "Marketing Department",
          buildingId: "cbba003",
          programs: [
            {
              id: "cbaa-bsbamm-adv",
              name: "Bachelor of Science in Business Administration, major in Marketing Management - Advertising",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "cbaa-bsbamm-dm",
              name: "Bachelor of Science in Business Administration, major in Marketing Management - Digital Marketing",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "cbaa-bsbae",
              name: "Bachelor of Science in Business Administration, major in Entrepreneurship",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
      ],
    },    {
      id: "coe",
      name: "College of Engineering",
      mainBuildingId: "COE",
      buildingIds: ["COE"],
      photos: [
        "https://images.unsplash.com/photo-**********-592deb58ef4e?q=80&w=1200&auto=format&fit=crop",
        "https://images.unsplash.com/photo-**********-d5d88e9218df?q=80&w=1200&auto=format&fit=crop",
      ],
      photoCredits: ["Photo by Unsplash", "Photo by Unsplash"],
      departments: [
        {
          id: "coe-abe",
          name: "Agricultural and Biosystems Engineering Department",
          buildingId: "COE",
          programs: [
            {
              id: "coe-bsabe",
              name: "Bachelor of Science in Agricultural and Biosystems Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Chemistry prerequisites",
              ],
            },
          ],
        },
        {
          id: "coe-che",
          name: "Chemical Engineering Department",
          buildingId: "COE",
          programs: [
            {
              id: "coe-bsche",
              name: "Bachelor of Science in Chemical Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Chemistry prerequisites",
              ],
            },
          ],
        },        {
          id: "coe-ce",
          name: "Civil Engineering Department",
          buildingId: "COE",
          requirements: [
            "Strong foundation in Mathematics and Physics",
            "Spatial visualization skills",
            "Problem-solving aptitude",
            "Interest in infrastructure development",
          ],
          photos: [
            "https://images.unsplash.com/photo-1504307651254-35680f356dfd?q=80&w=1200&auto=format&fit=crop",
            "https://images.unsplash.com/photo-1581094794329-c8112a89af12?q=80&w=1200&auto=format&fit=crop",
          ],
          photoCredits: [
            "Photo by Unsplash - Civil Engineering",
            "Photo by Unsplash - Construction Site",
          ],
          programs: [
            {
              id: "coe-bsce",
              name: "Bachelor of Science in Civil Engineering",
              description:
                "This program prepares students to design, construct, and maintain infrastructure projects such as buildings, bridges, roads, and water systems. Students learn structural analysis, construction management, and sustainable engineering practices.",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
          ],
        },
        {
          id: "coe-eee",
          name: "Electrical and Electronics Engineering Department",
          buildingId: "COE",
          programs: [
            {
              id: "coe-bsee",
              name: "Bachelor of Science in Electrical Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
            {
              id: "coe-bsece",
              name: "Bachelor of Science in Electronics Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
          ],
        },
        {
          id: "coe-me",
          name: "Mechanical Engineering Department",
          buildingId: "COE",
          programs: [
            {
              id: "coe-bsme",
              name: "Bachelor of Science in Mechanical Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
          ],
        },
      ],
    },    {
      id: "cnsm",
      name: "College of Natural Sciences and Mathematics",
      mainBuildingId: "CNSM-New_Building",
      buildingIds: ["CNSM-New_Building"],
      photos: [
        "https://lh3.googleusercontent.com/gps-cs/AIky0YWn2Gsn4Fw9-TZqO2G6Fe4CWrsRwLbKG6sWX_uoT-6VopbZi0sFw7dKmwxaB0O5JaUDtZhXXeVkwtxub_XDeUeP89F6QaH2d8ou0arVv3NzSjAM-NbJLRKeK-fB4H7LviKXG9f7=w1050-h806-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YXFxbd3z-0zTbGp4ybMCsTvU4fL-naHWMqKQ8t5p-CMiqnDkHbidXh9ewokvz7nyDRYQEIFmJezX7QIslcQQxFURr_fyrx_LbIxYd6ZVWVo-QmWRhoasC12XjjUXE-r5j4q8_E1SQ=w750-h1235-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nord9QUz7rQGF-OWWFuXrAnXY_etatm6s65R6V1YDpL3AS9eXWH3HHtJ4sYVWpCnbG7KgUCpSUVS5Fy8fKCFRVri87A9hda6fXQUmLHuGXUQ0sE2JTaqMxTqHpD36AkOlmNukKS=w1003-h952-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nqoJEySSMmGrnRUq8hCAe4UN63FUVxoZG0cJY3fIpvO3oXDDZQdKPRxbUfV5NdCvWMvO-StLmFeuE1MeIVd25OmWefsMnUXUGZJ1GQY-S0QC71nDYWmbN1kiORB2CaHKVj77DQ=s1063-k-no",
      ],
      photoCredits: [
        "Charliemagne Reyes",
        "Charliemagne Reyes", 
        "Cij Encabo",
        "Cij Encabo",
      ],
      departments: [
        {
          id: "cnsm-biology",
          name: "Biology Department",
          buildingId: "CNSM-New_Building",
          programs: [
            {
              id: "cnsm-bsbio",
              name: "Bachelor of Science in Biology (Animal Biology)",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Science Aptitude Test",
                "Biology and Chemistry prerequisites",
              ],
            },
            {
              id: "cnsm-msbio",
              name: "Master of Science in Biology",
              requirements: [
                "Bachelor's degree in Biology or related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
            {
              id: "cnsm-phdbio",
              name: "Doctor of Philosophy in Biology",
              requirements: [
                "Master's degree in Biology or related field",
                "Good Academic Standing",
                "Comprehensive Examination",
                "Dissertation Proposal",
                "Research Publications",
              ],
            },
          ],
        },
        {
          id: "cnsm-chemistry",
          name: "Chemistry Department",
          buildingId: "CNSM-New_Building",
          programs: [
            {
              id: "cnsm-bschem",
              name: "Bachelor of Science in Chemistry",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Science Aptitude Test",
                "Chemistry and Mathematics prerequisites",
              ],
            },
          ],
        },
        {
          id: "cnsm-mathematics",
          name: "Mathematics Department",
          buildingId: "CNSM-New_Building",
          programs: [
            {
              id: "cnsm-bsmath",
              name: "Bachelor of Science in Mathematics",
              requirements: [
                "High School Diploma with strong Mathematics background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Mathematics Aptitude Test",
              ],
            },
            {
              id: "cnsm-bsstat",
              name: "Bachelor of Science in Statistics",
              requirements: [
                "High School Diploma with strong Mathematics background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Mathematics Aptitude Test",
              ],
            },
            {
              id: "cnsm-msmath",
              name: "Master of Science in Mathematics",
              requirements: [
                "Bachelor's degree in Mathematics or related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
            {
              id: "cnsm-msthsm",
              name: "Master of Science in Teaching High School Mathematics",
              requirements: [
                "Bachelor's degree in Mathematics or Education",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "cnsm-phdmath",
              name: "Doctor of Philosophy in Mathematics",
              requirements: [
                "Master's degree in Mathematics or related field",
                "Good Academic Standing",
                "Comprehensive Examination",
                "Dissertation Proposal",
              ],
            },
            {
              id: "cnsm-certstat",
              name: "Certificate in Statistics",
              requirements: [
                "Bachelor's degree in any field",
                "Basic Mathematics background",
              ],
            },
          ],
        },
        {
          id: "cnsm-physics",
          name: "Physics Department",
          buildingId: "CNSM-New_Building",
          programs: [
            {
              id: "cnsm-bsphys",
              name: "Bachelor of Science in Physics",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physics Aptitude Test",
                "Mathematics and Physics prerequisites",
              ],
            },
            {
              id: "cnsm-msphys",
              name: "Master of Science in Physics",
              requirements: [
                "Bachelor's degree in Physics or related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
            {
              id: "cnsm-phdphys",
              name: "Doctor of Philosophy in Physics",
              requirements: [
                "Master's degree in Physics or related field",
                "Good Academic Standing",
                "Comprehensive Examination",
                "Dissertation Proposal",
              ],
            },
          ],
        },
      ],
    },    {
      id: "cssh",
      name: "College of Social Sciences and Humanities",
      mainBuildingId: "cssh-e",
      buildingIds: ["cssh-e"],
      photos: [
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nqk4La2t9P7TKjSiDouQyZuf9S8taste90Rwu3N3bs9nY6ANaPcGyCReVOeTjMs16vw5YbHgHae92kZ4sk6zNUQNH-coYaBa13GYm_OPRiTR3t14V_UR0uXrCXxwVDHFFpnqDg=s1063-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nqsmDe4FQRhPYh6LE54ZCsF8RxcJOb7fDOw3nDIMy-P5__YVAeb7k9EV44nXFxK7ZKq32xrD5Y3ZdQ2fRSkQdyY6anuj27yGB0EGDhKaR39IRkqVnoHk_KoPEj0qUz7U3E3emRG4w=s1083-k-no",
      ],
      photoCredits: ["Cij Encabo", "Cij Encabo"],
      departments: [
        {
          id: "cssh-cms",
          name: "Department of Communication and Media Studies",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-bajour",
              name: "Bachelor of Arts in Journalism",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
                "Writing Portfolio",
              ],
            },
            {
              id: "cssh-bacms",
              name: "Bachelor of Arts in Communication and Media Studies (Media Education)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
            {
              id: "cssh-bsdevcom",
              name: "Bachelor of Science in Development Communication",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
          ],
        },
        {
          id: "cssh-english",
          name: "English Department",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-baels",
              name: "Bachelor of Arts in English Language Studies",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
            {
              id: "cssh-balcs",
              name: "Bachelor of Arts in Literary and Cultural Studies",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
            {
              id: "cssh-maelt",
              name: "Master of Arts in English Language Teaching",
              requirements: [
                "Bachelor's degree in English or related field",
                "Teaching Experience",
                "Good Academic Standing",
                "English Proficiency Certificate",
              ],
            },
          ],
        },
        {
          id: "cssh-filipino",
          name: "Filipino Department",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-bafil",
              name: "Batsilyer sa Arte sa Filipino",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Filipino Proficiency Test",
              ],
            },
            {
              id: "cssh-bapan",
              name: "Batsilyer sa Arte sa Panitikan",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Filipino Proficiency Test",
              ],
            },
            {
              id: "cssh-mafil-ling",
              name: "Master ng Sining sa Filipino medyor sa Linguwistika",
              requirements: [
                "Bachelor's degree in Filipino or related field",
                "Good Academic Standing",
                "Filipino Proficiency Certificate",
              ],
            },
            {
              id: "cssh-mafil-lit",
              name: "Master ng Sining sa Filipino medyor sa Literatura",
              requirements: [
                "Bachelor's degree in Filipino or related field",
                "Good Academic Standing",
                "Filipino Proficiency Certificate",
              ],
            },
          ],
        },
        {
          id: "cssh-history",
          name: "History Department",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-bahist",
              name: "Bachelor of Arts in History",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cssh-lis",
          name: "Department of Library and Information Science",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-blis",
              name: "Bachelor of Library and Information Science",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Literacy Test",
              ],
            },
          ],
        },
        {
          id: "cssh-philosophy",
          name: "Department of Philosophy",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-baphil",
              name: "Bachelor of Arts in Philosophy",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Critical Thinking Assessment",
              ],
            },
          ],
        },
        {
          id: "cssh-polsci",
          name: "Department of Political Studies",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-baps",
              name: "Bachelor of Arts in Political Science",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cssh-psychology",
          name: "Department of Psychology",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-bspsych",
              name: "Bachelor of Science in Psychology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Psychological Assessment",
                "Interview",
              ],
            },
          ],
        },
        {
          id: "cssh-sociology",
          name: "Department of Sociology",
          buildingId: "cssh-e",
          programs: [
            {
              id: "cssh-basoc",
              name: "Bachelor of Arts in Sociology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "ced",
      name: "College of Education",
      mainBuildingId: "ced",
      buildingIds: ["ced"],
      departments: [
        {
          id: "ced-elementary",
          name: "Elementary Teaching Department",
          buildingId: "ced",
          programs: [
            {
              id: "ced-beed",
              name: "Bachelor of Elementary Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "ced-beced",
              name: "Bachelor of Early Childhood Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
          ],
        },
        {
          id: "ced-secondary",
          name: "Secondary Teaching Department",
          buildingId: "ced",
          programs: [
            {
              id: "ced-bsed-sci",
              name: "BSEd Sciences",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-math",
              name: "BSEd Mathematics",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-eng",
              name: "BSEd English",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "English Proficiency Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-fil",
              name: "BSEd Filipino",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Filipino Proficiency Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-ss",
              name: "BSEd Social Studies",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
          ],
        },
        {
          id: "ced-homeec",
          name: "Department of Home Economics",
          buildingId: "ced",
          programs: [
            {
              id: "ced-btle",
              name: "Bachelor of Technology and Livelihood Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Skills Assessment",
              ],
            },
            {
              id: "ced-btvte",
              name: "Bachelor of Technical-Vocational Teacher Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Skills Assessment",
                "Teaching Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "ced-graduate",
          name: "Graduate Education Department",
          buildingId: "ced",
          programs: [
            {
              id: "ced-phdem",
              name: "Doctor of Philosophy in Educational Management",
              requirements: [
                "Master's degree in Education or related field",
                "Administrative Experience",
                "Comprehensive Examination",
                "Dissertation Proposal",
              ],
            },
            {
              id: "ced-maed-reading",
              name: "Master of Arts in Education major in Reading Program",
              requirements: [
                "Bachelor's degree in Education or related field",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "ced-maed-guidance",
              name: "Master of Arts in Education Major in Guidance and Counseling",
              requirements: [
                "Bachelor's degree in Education, Psychology or related field",
                "Counseling Experience (preferred)",
                "Good Academic Standing",
              ],
            },
            {
              id: "ced-maed-em",
              name: "Master of Arts in Education major in Educational Management",
              requirements: [
                "Bachelor's degree in Education or related field",
                "Administrative Experience (preferred)",
                "Good Academic Standing",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "det",
      name: "Division of Engineering Technology",
      mainBuildingId: "Division_of_Engineering_Technology",
      buildingIds: ["Division_of_Engineering_Technology"],
      departments: [
        {
          id: "det-graduate",
          name: "Graduate Studies",
          buildingId: "Division_of_Engineering_Technology",
          programs: [
            {
              id: "det-msiet-ce",
              name: "Master of Science in Industrial Engineering Technology Major in Construction Engineering",
              requirements: [
                "Bachelor's degree in Engineering Technology or related field",
                "Work Experience in Construction",
                "Good Academic Standing",
              ],
            },
            {
              id: "det-msiet-ere",
              name: "Master of Science in Industrial Engineering Technology Major in Electrical and Renewable Energy",
              requirements: [
                "Bachelor's degree in Engineering Technology or related field",
                "Work Experience in Electrical/Energy field",
                "Good Academic Standing",
              ],
            },
            {
              id: "det-msiet-ms",
              name: "Master of Science in Industrial Engineering Technology Major in Materials Science",
              requirements: [
                "Bachelor's degree in Engineering Technology or related field",
                "Work Experience in Materials field",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "det-metal",
          name: "Metal Department",
          buildingId: "Division_of_Engineering_Technology",
          programs: [
            {
              id: "det-det-mst",
              name: "Diploma in Engineering Technology Major in Machine Shop Technology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-detre",
              name: "Diploma in Electrical Engineering Technology and Renewable Energy",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-bset-maf",
              name: "Bachelor of Science in Engineering Technology Major in Machining and Fabrication",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-bset-ere",
              name: "Bachelor of Science in Engineering Technology Major in Electrical and Renewable Energy",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "det-wood",
          name: "Wood Department",
          buildingId: "Division_of_Engineering_Technology",
          programs: [
            {
              id: "det-dct",
              name: "Diploma in Engineering Technology Major in Construction Technology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-bset-cet",
              name: "Bachelor of Science in Engineering Technology Major in Construction Engineering Technology",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cfas",
      name: "College of Fisheries and Aquatic Sciences",
      mainBuildingId: "element633",
      buildingIds: ["element633"],
      departments: [
        {
          id: "cfas-fisheries",
          name: "Fisheries Department",
          buildingId: "element633",
          programs: [
            {
              id: "cfas-bsfish",
              name: "BS Fisheries",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Science Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cfas-fishtech",
          name: "Fisheries Technology Department",
          buildingId: "element633",
          programs: [
            {
              id: "cfas-dtaqua",
              name: "DT in Aquaculture",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "cfas-dtfp",
              name: "DT in Fish Processing",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cfes",
      name: "College of Forestry and Environmental Studies",
      mainBuildingId: "element1900",
      buildingIds: ["element1900"],
      departments: [
        {
          id: "cfes-graduate",
          name: "Graduate School",
          buildingId: "element1900",
          programs: [
            {
              id: "cfes-msf",
              name: "Master of Science in Forestry",
              requirements: [
                "Bachelor's degree in Forestry or related field",
                "Work Experience in Forestry",
                "Good Academic Standing",
              ],
            },
            {
              id: "cfes-mesf",
              name: "Master in Ecogovernance and Social Forestry",
              requirements: [
                "Bachelor's degree in Forestry, Environmental Science or related field",
                "Work Experience in Environmental/Forestry field",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "cfes-envstudies",
          name: "Department of Environmental Studies",
          buildingId: "element1900",
          programs: [
            {
              id: "cfes-bsenvs",
              name: "BS Environmental Science",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Environmental Science Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cfes-forestry",
          name: "Department of Forestry",
          buildingId: "element1900",
          programs: [
            {
              id: "cfes-bsfor",
              name: "BS Forestry",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Forestry Aptitude Test",
              ],
            },
            {
              id: "cfes-bsfor-agro",
              name: "BS Forestry Major in Agroforestry",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Forestry Aptitude Test",
              ],
            },
          ],
        },
      ],
    },    {
      id: "chs",
      name: "College of Health Sciences",
      mainBuildingId: "element144",
      buildingIds: ["element144"],
      photos: [
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nrpN3_OdQ1KUndws3Zxe35kdLcRWyX36uwE6oCVNKAhrsyxaskeU_Hmfvz-3fgnWBGUKq48QPjqiK9ZcM0B5dGBqVK2ocqIeFmENNPFEWyOM3ZUyKhYVD9PoQBnAxgxzBjTNiBHpQ=s720-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nrQla5woJ-5ZlV6jOdoszWwjDCc527QCrgqYI3uO8xgaxkY91esD55LBYjF7kjL8D8j0g_2MzgnA6r8FZkgFvLv07j3Ck0hb1gvyb8iMxXY05wHVZF2g6QmnykxBhvZsfE343O8=s946-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nokuVWlO5YoUGklGZLFhmNk1MdTh8hT7QctuMi3Wjp_xVNixuiiWfEaDpv8RXZtjGK3131ny6Ta_9eOVLjRqVEHTPUP1PU_OzW6MCzhNKMOyYy4O_Dh1VoLkixfrLXs9aVPxVUFJw=s638-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nqJ9FPJbte1c6GD1_bkUk7HD_O1ya1vvKjE3b4TDmYUlW6gZYo_8V72J_lFLwX1Suz5G9q5klCUObbGJLBzXXRFSXS4RX9gz7SIXzosBZHuFxiZtq5vWGInML3YiYf68z2Da1Uu=s480-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nq9fllSoNTH2t6YNJQeGPyPBDOEiynam4URt2usAhI_9ZGIs69bJT70gaaCrZzpzPqZ6St50PZK2UB-waa8pj8vG6W6H1o2d4M2CFy-12CrAe6ysVJ3ji8pRS7RTddVFH4R7SYA=s1044-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nquZINDSo26ZS1SkEj4Z7CzUQv-ru4cYei3N1UaNWmHIB-C7IxzinQlj_yyiNkzfkBFP0LktcvcwufKiEMGCQoQXxqDIe0uRlhiHa9At24QuBajWZcH4dKcvJBNSzNnsdgfRaLe=s480-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4npqfdrhozv8vjrIU4jDV1jzf9ZH2YbQK6rFVZdggA6fWZ7L6HQVTmONwJuYuOA1wSF8vjHCvn7uOEYUJqrYcuG4OhMvsnjshpCtQgdH2m1TzqKPj9T25Zxms5wSuD47GiSxjoWSbQ=s480-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nq5Q6YjQplAp6dbJZiayG61lkDIwVuUFGxX6EAhF-Pu0kI4y68Pui4ZRiJOq7WB-h1S-1yorVr6143Uj8jA-zgs3YVE6uXk_aUnfTQpbXX2NHaBZXHcGTwVEdlY91Yqezvnt2P9=s480-k-no",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4noszRGFK78lmHyB9tCBGZZMjcaPSFnCF2hHTXxs7lvuzM8fy8TRFxQEo0yuCEGL7vNNxoi_O1KQTlhILRjnqNCBts_uwinUKkUepBEdERAd0v6CxTeE-2L3FfcmylSD8ZOJtPUR",
        "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nrX-DPxq24WmoTwdG5RZZcE0xmDGa_e4k_s6ezFZosM0dLyLI1ExcbND6cqKUnILPVUkf5Mr0zhDOMvfuZwT_H1ubgV8KSm_JkKEqq9Yahu1PhWFCRimzXwjPg8M2bGnN1O0mVZBw=s1244-k-no",
      ],
      photoCredits: [
        "Anjasha Macapodi",
        "Alimar Abdulhalim",
        "Anjasha Macapodi",
        "Abdul Azis Camid",
        "Anjasha Macapodi",
        "Abdul Azis Camid",
        "Abdul Azis Camid",
        "Abdul Azis Camid",
        "Abdul Azis Camid",
        "Anjasha Macapodi",
      ],
      departments: [
        {
          id: "chs-nursing",
          name: "Nursing Department",
          buildingId: "element144",
          programs: [
            {
              id: "chs-bsn",
              name: "Bachelor of Science in Nursing",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Nursing Aptitude Test",
                "Interview",
                "Physical and Mental Health Assessment",
              ],
            },
          ],
        },
        {
          id: "chs-graduate",
          name: "Graduate Department",
          buildingId: "element144",
          programs: [
            {
              id: "chs-msn",
              name: "Master of Science in Nursing",
              requirements: [
                "Bachelor's degree in Nursing",
                "RN License",
                "Clinical Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "chs-man",
              name: "Master of Arts in Nursing",
              requirements: [
                "Bachelor's degree in Nursing",
                "RN License",
                "Clinical Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "chtm",
      name: "College of Hotel and Restaurant Management",
      mainBuildingId: "element034",
      buildingIds: ["element034"],
      departments: [
        {
          id: "chtm-hospitality",
          name: "Hospitality Management Department",
          buildingId: "element034",
          programs: [
            {
              id: "chtm-bshm",
              name: "Bachelor of Science in Hospitality Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Interview",
                "Basic English Communication Skills",
              ],
            },
          ],
        },
        {
          id: "chtm-tourism",
          name: "Tourism Management Department",
          buildingId: "element034",
          programs: [
            {
              id: "chtm-bstm",
              name: "Bachelor of Science in Tourism Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Interview",
                "Basic English Communication Skills",
              ],
            },
          ],
        },
      ],
    },    {
      id: "cics",
      name: "College of Information and Computing Sciences",
      mainBuildingId: "College_of_Information_and_Computing_Sciences",
      buildingIds: ["College_of_Information_and_Computing_Sciences"],
      photos: [
        "https://lh3.googleusercontent.com/gps-cs/AIky0YXjuEk2PlNOOblJvzZmmeQPL3RjoPFZafrK1XuLlD0G-ect29dfLIyEdGCX5QpiPpqRa2xoR9LkZNqPJc-xhiPZYMgNgfIgorRtVbPwtlIYfsXNpHWkohiRi9s8kbkZemObtEQ=w1050-h825-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YV8_86DFOTC7tFefJx_z3r0IfQJR3QeE1drlWv7I_SB74vmcfJDfB80ZxmJiNtMQ3yzpLu-Arznx5mAJCIJyZrEcuJ3ktcLWYoYEthxracR4yr7Lti-W7jGTwgXweB7XVGi_pri=w750-h1068-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YW9Lo4ybOTgvF3X1TyEcs7KjydR9srAZjjkTkxkVEssJi6QYWK0LSbd7-_JEh5TuR3dLuxBlxImEasNxPJXsN3GEcKIrf8JC5yapW4avO9sN_8aT6CjHgNsSksU_k8Gb4fB348DIoWGw7lU=w750-h1235-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YWbWiVbTIg2zgnSml5xaQJccc-rp8F2NdTI31Kz6c2gimqngopC7Lx2LgfX5TPzeAc-ctvOczIZhcHPy0CsBfYIM8MfsXbuIKqd6Mq38A5FZ7yqQN2E64CIpx_Rbp_n_fIVK4aJuf0F9mrE=w750-h1235-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YX5iqejtEDmllrp8Y0RpBvbFSyPZvR2Lnqvnr8Oo_oxzmukhOgtmbuGfWtccs-M6X7nbDvUPMgYpFBwhXPa3DMFO6jCmvbdV8ahvwukQ35lxS0YpSX0f6OFApjobCAfxgMEIDAjLYj8FIYI=w750-h1235-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YXiauMY4MivOTPw_CVV51LTBMoY-LkTnPsWBcpQuxUqSXkUOKjCpoKfKb6ewVwdJi0ze9ehXHcGXe-s4Gh_h6F2fjOneYA7G1HcnVuTIgiWeVxQPJzcPypcr8gP6Sr6qA9D1A3MQOBqDFY=w750-h606-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YVedz0rD-gOL7VJO0DCoNlU1S4Xns_THmwhjTOJ7zeZLj66U6nBsbF384NO9lMUBnLhfb_glt2ZIixUdivEH0uIl5YxGlzKYhqOiTQha4BBH1QDyGgLhNY3oOV67iLeZwcdZ7lA1UQj6nsq=w750-h1235-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YWB-DSv--5u5-m2pzNejr2qkeUy_zL8zeuwIFMats3KNnZQzoQZZAIdDItXqR1i0WGhaJx_gcAdIcdmwhmWKHP9kwcDtLL1ia-DkV2f4Rd45b8ETlqwW-Hgwn7jZfYi9P1_RkjIUGHYYXF8=w750-h1235-p-k-no",
        "https://lh3.googleusercontent.com/gps-cs/AIky0YXkVbt59l1IUgvABOv36L79xkEGTVvxJi0J2-79fiWDngcbPviw7Wiru3WFx7Z9ehIU4QQGFhRxfLDDBp-7Vyyn_7iRwofMiaSXFLYWxwIpmOEn3edbDQ5Jb2Ul90H4o97DyDcfzf2UYL48=w750-h1235-p-k-no",
      ],
      photoCredits: [
        "Amal Sultan",
        "Amal Sultan",
        "Mojahed Mipanga",
        "Mojahed Mipanga",
        "Mojahed Mipanga",
        "Mojahed Mipanga",
        "Mojahed Mipanga",
        "Mojahed Mipanga",
        "Mojahed Mipanga",
      ],
      departments: [        {
          id: "cics-cs",
          name: "Computer Science Department",
          buildingId: "College_of_Information_and_Computing_Sciences",
          requirements: [
            "Strong mathematical foundation",
            "Logical thinking and problem-solving skills",
            "Interest in technology and programming",
            "Basic computer literacy",
          ],
          photos: [
            "https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?q=80&w=1200&auto=format&fit=crop",
            "https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?q=80&w=1200&auto=format&fit=crop",
            "https://images.unsplash.com/photo-1550751827-4bd374c3f58b?q=80&w=1200&auto=format&fit=crop",
          ],
          photoCredits: [
            "Photo by Unsplash - Computer Lab",
            "Photo by Unsplash - Programming",
            "Photo by Unsplash - Tech Workspace",
          ],
          programs: [
            {
              id: "cics-bscs",
              name: "Bachelor of Science in Computer Science",
              description:
                "This comprehensive program covers software development, algorithms, data structures, artificial intelligence, and computer systems. Students learn to solve complex problems using computational thinking and modern programming technologies.",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Computer Programming Aptitude Test",
                "Mathematics Proficiency Test",
              ],
            },
          ],
        },
        {
          id: "cics-is",
          name: "Department of Information Sciences",
          buildingId: "College_of_Information_and_Computing_Sciences",
          programs: [
            {
              id: "cics-bsis",
              name: "Bachelor of Science in Information Systems",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Systems Aptitude Test",
              ],
            },
            {
              id: "cics-bsit-db",
              name: "Bachelor of Science in Information Technology (Database)",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Technology Aptitude Test",
              ],
            },
            {
              id: "cics-bsit-net",
              name: "Bachelor of Science in Information Technology (Network)",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Technology Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "kfciaas",
      name: "King Faisal Center for Islamic Arabic and Asian Studies",
      mainBuildingId: "element097",
      buildingIds: ["element097"],
      departments: [
        {
          id: "kfciaas-graduate",
          name: "Graduate Program Department",
          buildingId: "element097",
          programs: [
            {
              id: "kfciaas-mais",
              name: "Master of Arts in Islamic Studies major in Muslim Law",
              requirements: [
                "Bachelor's degree in Islamic Studies or related field",
                "Good Academic Standing",
                "Islamic Studies Background",
                "Arabic Language Proficiency",
              ],
            },
          ],
        },
        {
          id: "kfciaas-ir",
          name: "International Relations Department",
          buildingId: "element097",
          programs: [
            {
              id: "kfciaas-bsir",
              name: "Bachelor of Science in International Relations",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "kfciaas-is",
          name: "Islamic Studies Department",
          buildingId: "element097",
          programs: [
            {
              id: "kfciaas-bais",
              name: "Bachelor of Arts in Islamic Studies major in Shari'ah",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Islamic Studies Background",
                "Arabic Language Proficiency",
              ],
            },
          ],
        },
        {
          id: "kfciaas-arabic",
          name: "Teaching Arabic Department",
          buildingId: "element097",
          programs: [
            {
              id: "kfciaas-bsta",
              name: "Bachelor of Science in Teaching Arabic",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Arabic Language Proficiency",
                "Teaching Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "col",
      name: "College of Law",
      mainBuildingId: "col",
      buildingIds: ["col"],
      departments: [
        {
          id: "col-law",
          name: "College of Law",
          buildingId: "col",
          programs: [
            {
              id: "col-jd",
              name: "Juris Doctor (JD) program",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "Law Aptitude Test",
                "Interview",
                "Character and Fitness Evaluation",
              ],
            },
            {
              id: "col-shariah",
              name: "Shari'ah",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "Islamic Law Aptitude Test",
                "Islamic Studies Background",
                "Arabic Language Proficiency",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cpa",
      name: "College of Public Affairs",
      mainBuildingId: "cpa",
      buildingIds: ["cpa"],
      departments: [
        {
          id: "cpa-graduate",
          name: "Graduate Studies",
          buildingId: "cpa",
          programs: [
            {
              id: "cpa-dpa",
              name: "Doctor of Public Administration (DPA) Program",
              requirements: [
                "Master's degree in Public Administration or related field",
                "Administrative Experience",
                "Research Experience",
                "Comprehensive Examination",
              ],
            },
            {
              id: "cpa-mscd",
              name: "Master of Science in Community Development (MSCD) Program",
              requirements: [
                "Bachelor's degree in Social Sciences or related field",
                "Community Development Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "cpa-mssw",
              name: "Master of Science in Social Work (MSSW) Program",
              requirements: [
                "Bachelor's degree in Social Work or related field",
                "Social Work Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "cpa-pa",
          name: "Public Administration",
          buildingId: "cpa",
          programs: [
            {
              id: "cpa-om",
              name: "Organization and Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Management Aptitude Test",
              ],
            },
            {
              id: "cpa-hrm",
              name: "Human Resource Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Management Aptitude Test",
              ],
            },
            {
              id: "cpa-lrga",
              name: "Local and Regional Government Administration",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Public Administration Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cpa-sw",
          name: "Social Work Department",
          buildingId: "cpa",
          programs: [
            {
              id: "cpa-bssw",
              name: "Bachelor of Science in Social Work (BSSW)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Work Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "cpa-msw",
              name: "Master in Social Work (MSW)",
              requirements: [
                "Bachelor's degree in Social Work or related field",
                "Social Work Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "cpa-mssw-prog",
              name: "Master of Science in Social Work (MSSW)",
              requirements: [
                "Bachelor's degree in Social Work or related field",
                "Social Work Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "cpa-scd",
          name: "Sustainable Community Development",
          buildingId: "cpa",
          programs: [
            {
              id: "cpa-fcd",
              name: "Foundations of Community Development",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Community Development Interest",
              ],
            },
            {
              id: "cpa-isd",
              name: "Introduction to Sustainable Development",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Environmental Awareness",
              ],
            },
            {
              id: "cpa-ppsc",
              name: "Policies, Programs and Services in Community Development",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Science Background",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "csper",
      name: "College of Sports Physical Education and Recreation",
      mainBuildingId: "CSPEAR_and_Grandstand",
      buildingIds: ["CSPEAR_and_Grandstand"],
      departments: [
        {
          id: "csper-professional",
          name: "Department of Professional Studies",
          buildingId: "CSPEAR_and_Grandstand",
          programs: [
            {
              id: "csper-bspe",
              name: "Bachelor of Science in Physical Education (BSPE)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physical Fitness Test",
                "Sports Skills Assessment",
              ],
            },
            {
              id: "csper-pdpe",
              name: "Professional Diploma in Physical Education (PDPE)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physical Fitness Test",
              ],
            },
            {
              id: "csper-mspe",
              name: "Master of Science in Physical Education (MSPE)",
              requirements: [
                "Bachelor's degree in Physical Education or related field",
                "Teaching/Coaching Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "csper-pe",
          name: "Department of Physical Education",
          buildingId: "CSPEAR_and_Grandstand",
          programs: [
            {
              id: "csper-bspe-dept",
              name: "Bachelor of Science in Physical Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physical Fitness Test",
                "Sports Skills Assessment",
              ],
            },
          ],
        },
      ],
    },
  ],
  buildings: [
    {
      id: "coa007",
      name: "College of Agriculture Main Building",
      acronym: "coa",
      type: "main",
      position: { x: -9, y: 1, z: 95 },
      size: { w: 0, h: 0, d: 0 },
      color: "#079631",
      rooms: ["AGRI-101", "AGRI-102", "AGRI-LAB-1", "DEAN-OFFICE"],
      description:
        "The main hub for agricultural education and research at MSU. Houses state-of-the-art laboratories, research facilities, and administrative offices for all agriculture-related programs.",
      yearBuilt: 1985,
      floors: 4,      
      contactInfo: {
        phone: "+63 (65) 341-3156",
        email: "<EMAIL>",
        office: "Dean's Office, 4th Floor",
      },
    },
    {
      id: "coa005",
      name: "Plant Science Department Building",
      acronym: "psdb",
      type: "additional",
      position: { x: -28, y: 1, z: 89 },
      size: { w: 0, h: 0, d: 0 },
      color: "#079631",
      rooms: ["PLANTSCI-101", "PLANTSCI-LAB-1"],
    },
    {
      id: "coa008",
      name: "Animal Science Department Building",
      acronym: "asdb",
      type: "additional",
      position: { x: -5, y: 2, z: 90 },
      size: { w: 0, h: 0, d: 0 },
      color: "#079631",
      rooms: ["ANIMALSCI-101", "ANIMALSCI-LAB-1"],
    },
    {
      id: "cbba003",
      name: "College of Business Administration and Accountancy Main Building",
      acronym: "cbaa",
      type: "main",
      position: { x: -14, y: 1, z: 65 },
      size: { w: 0, h: 0, d: 0 },
      color: "#ffdf00",
      rooms: [
        "ACCT-101",
        "ACCT-102",
        "BUS-201",
        "BUS-202",
        "DEAN-301",
        "CONF-ROOM",
        "LIBRARY"
      ], // Sample CBAA rooms
      description:
        "Modern business education facility featuring cutting-edge technology and collaborative learning spaces. Home to all business and accountancy programs with industry-standard facilities.",
      yearBuilt: 1992,
      floors: 5,      contactInfo: {
        phone: "+63 (65) 341-3157",
        email: "<EMAIL>",
        office: "Dean's Office, 5th Floor",
      },
    },
    {
      id: "COE",
      name: "College of Engineering Main Building",
      acronym: "coe",
      type: "main",
      position: { x: 40, y: 1, z: 71 },
      size: { w: 0, h: 0, d: 0 },
      color: "#ff0000",
      rooms: [
        "EMB-101",
        "EMB-102",
        "EMB-201",
        "EMB-202",
        "EMB-301",
        "EMB-302",
        "LAB-01",
        "LAB-02",
        "DEAN-OFFICE"
      ], // Sample engineering rooms
      description:
        "Premier engineering education facility equipped with advanced laboratories and workshops. Supports all engineering disciplines with state-of-the-art equipment and research facilities.",
      yearBuilt: 1988,
      floors: 6,      contactInfo: {
        phone: "+63 (65) 341-3158",
        email: "<EMAIL>",
        office: "Dean's Office, 6th Floor",
      },
    },    {
      id: "CNSM-New_Building",
      name: "College of Natural Sciences and Mathematics Main Building",
      acronym: "cnsm",
      type: "main",
      position: { x: 45, y: 1, z: -29 },
      size: { w: 1, h: 1, d: 1 },
      color: "#ed2024",
      rooms: [], // To be filled based on 3D object names
    },{
      id: "cssh-e",
      name: "College of Social Sciences and Humanities Main Building",
      acronym: "cssh",
      type: "main",
      position: { x: 42, y: 1, z: -0 },
      size: { w: 0, h: 0, d: 0 },
      color: "#ffc637",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "ced",
      name: "College of Education Main Building",
      acronym: "ced",
      type: "main",
      position: { x: 18, y: 1, z: 74 },
      size: { w: 0, h: 0, d: 0 },
      color: "#067be5",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "Division_of_Engineering_Technology",
      name: "Division of Engineering Technology Main Building",
      acronym: "det",
      type: "main",
      position: { x: -39, y: 2, z: -20 },
      size: { w: 0, h: 0, d: 0 },
      color: "#FF271B",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element633",
      name: "College of Fisheries and Aquatic Sciences Main Building",
      acronym: "cfas",
      type: "main",
      position: { x: 84, y: 1, z: -89 },
      size: { w: 0, h: 0, d: 0 },
      color: "#1ac8d0",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element1900",
      name: "College of Forestry and Environmental Studies Main Building",
      acronym: "cfes",
      type: "main",
      position: { x: 87, y: 1, z: -76 },
      size: { w: 0, h: 0, d: 0 },
      color: "#4c6839",
      rooms: [], // To be filled based on 3D object names
    },    {
      id: "element144",
      name: "College of Health Sciences Main Building",
      acronym: "chs",
      type: "main",
      position: { x: 212, y: 1, z: -76 },
      size: { w: 0, h: 0, d: 0 },
      color: "#34762c",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element034",
      name: "College of Hotel and Restaurant Management",
      acronym: "charm",
      type: "main",
      position: { x: 40, y: 1, z: -102 },
      size: { w: 0, h: 0, d: 0 },
      color: "#ff3470",
      rooms: [], // To be filled based on 3D object names
    },    {
      id: "College_of_Information_and_Computing_Sciences",
      name: "College of Information and Computing Sciences Main Building",
      acronym: "cics",
      type: "main",
      position: { x: -15, y: 2, z: -73 },
      size: { w: 0, h: 0, d: 0 },
      color: "#2e3192",
      description:
        "The main hub for information and computing sciences at MSU. Houses state-of-the-art laboratories, research facilities, and administrative offices for all computing-related programs.",
      rooms: [
        "RM300",
        "RM301",
        "RM302"
      ],
    },
    {
      id: "element097",
      name: "King Faisal Center for Islamic Arabic and Asian Studies Main Building",
      acronym: "kfciaas",
      type: "main",
      position: { x: 152, y: 1, z: -60 },
      size: { w: 0, h: 0, d: 0 },
      color: "#2f936b",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "col",
      name: "College of Law Main Building",
      acronym: "col",
      type: "main",
      position: { x: 10, y: 1, z: 82 },
      size: { w: 0, h: 0, d: 0 },
      color: "#6d1d52",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "cpa",
      name: "College of Public Affairs Main Building",
      acronym: "cpa",
      type: "main",
      position: { x: 22, y: 1, z: 36 },
      size: { w: 0, h: 0, d: 0 },
      color: "#006ee3",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "CSPEAR_and_Grandstand",
      name: "College of Sports Physical Education and Recreation Main Building",
      acronym: "cspear",
      type: "main",
      position: { x: -33, y: 1, z: 18 },
      size: { w: 0, h: 0, d: 0 },
      color: "#6c1a24",
      rooms: [], // To be filled based on 3D object names
    },
  ],
};

// Helper functions for the new data structure
export function findCollegeById(buildingId: string): College | null {
  return campus.colleges.find((c) => c.id === buildingId) || null;
}

export function findBuildingById(buildingId: string): Building | null {
  return campus.buildings.find((b) => b.id === buildingId) || null;
}

export function getDepartmentByBuilding(buildingId: string): Department | null {
  // Get the department by building id in campus.colleges
  const college = getCollegeByBuilding(buildingId);
  if (!college) return null;

  return college.departments.find((dept) => dept.buildingId === buildingId) || null;
}

export function getDepartmentsByCollege(collegeId: string): Department[] {
  const college = findCollegeById(collegeId);
  if (!college) return [];
  return college.departments;
}

// New helper: aggregate all programs offered under a college across its departments
export function getProgramsByCollege(collegeId: string): Program[] {
  const college = getCollegeById(collegeId) || campus.colleges.find((c) => c.id === collegeId);
  if (!college) return [];
  return college.departments.flatMap((dept) => dept.programs || []);
}

export function getCollegeByBuilding(buildingId: string): College | null {
  // First, find the building to understand its relationship
  const building = findBuildingById(buildingId);
  if (!building) return null;

  // Find college by checking both mainBuildingId and buildingIds
  return (
    campus.colleges.find((college) => {
      // Check if this building is the main building
      if (college.mainBuildingId === building.id) {
        return true;
      }
      // Check if this building is in the college's additional buildings
      if (college.buildingIds.includes(building.id)) {
        return true;
      }
      // For backwards compatibility, also check building.buildingId against college.id
      if (building.id === college.id) {
        return true;
      }
      return false;
    }) || null
  );
}

export function findDepartmentById(
  departmentId: string
): { department: Department; college: College } | null {
  for (const college of campus.colleges) {
    const department = college.departments.find((d) => d.id === departmentId);
    if (department) {
      return { department, college };
    }
  }
  return null;
}

export function findProgramById(
  programId: string
): { program: Program; department: Department; college: College } | null {
  for (const college of campus.colleges) {
    for (const department of college.departments) {
      const program = department.programs.find((p) => p.id === programId);
      if (program) {
        return { program, department, college };
      }
    }
  }
  return null;
}

export function findRoomById(roomId: string) {
  const id = roomId.trim().toUpperCase();
  for (const building of campus.buildings) {
    const room = building.rooms.find((r) => r.toUpperCase() === id);
    if (room) {
      const world = {
        x: building.position.x + 0,
        y: building.position.y + 0 + building.size.h / 2,
        z: building.position.z + 0,
      };
      return { room, building, world } as const;
    }
  }
  return null;
}

export function getBuildingsByCollege(buildingId: string): Building[] {
  return campus.buildings.filter((b) => b.id === buildingId);
}

export function getMainBuildingByCollege(buildingId: string): Building | null {
  return (
    campus.buildings.find(
      (b) => b.id === buildingId && b.type === "main"
    ) || null
  );
}

// New helper function: Get college by college ID (not building ID)
export function getCollegeById(collegeId: string): College | null {
  return campus.colleges.find((c) => c.id === collegeId) || null;
}

// New helper function: Get all buildings for a specific college
export function getAllBuildingsForCollege(collegeId: string): Building[] {
  const college = getCollegeById(collegeId);
  if (!college) return [];

  return campus.buildings.filter((building) => {
    // Check if building is the main building
    if (building.id === college.mainBuildingId) return true;
    // Check if building is in additional buildings
    if (college.buildingIds.includes(building.id)) return true;
    // Legacy check: building.buildingId matches college.id
    if (building.id === college.id) return true;
    return false;
  });
}

// Improved function: Get the main building for a college by college ID
export function getMainBuildingForCollege(collegeId: string): Building | null {
  const college = getCollegeById(collegeId);
  if (!college) return null;

  return campus.buildings.find((b) => b.id === college.mainBuildingId) || null;
}

// New helper function: Check if a building is the main building for its college
export function isBuildingMainForCollege(buildingId: string): boolean {
  const building = findBuildingById(buildingId);
  if (!building) return false;

  const college = getCollegeByBuilding(buildingId);
  if (!college) return false;

  return college.mainBuildingId === building.id;
}

// New helper function: Get department buildings for clickable navigation
export function getDepartmentBuildings(
  buildingId: string
): Array<{
  departmentName: string;
  departmentId: string;
  buildingId: string;
  buildingName: string;
}> {
  const college = getCollegeByBuilding(buildingId);
  if (!college) return [];

  const departmentBuildings = new Map<
    string,
    {
      departmentId: string;
      departmentName: string;
      buildingId: string;
      buildingName: string;
    }[]
  >();
  
  college.departments.forEach((dept) => {
    const building = findBuildingById(dept.buildingId);
    if (building) {
      if (!departmentBuildings.has(dept.id)) {
        departmentBuildings.set(dept.id, []);
      }

      departmentBuildings.get(dept.id)?.push({
        departmentId: dept.id,
        departmentName: dept.name,
        buildingId: dept.buildingId,
        buildingName: building.name,
      });
    }
  });
  
  return Array.from(departmentBuildings.values()).flat();
}
