// Simple test script to verify room search functionality
const { campus } = require('./src/data/campus.ts');

console.log('Testing room search functionality...\n');

// Test 1: Search for a room that exists
console.log('Test 1: Searching for "RM300"');
const results1 = [];
campus.buildings.forEach(building => {
  building.rooms.forEach(room => {
    if (room.toLowerCase().includes('rm300')) {
      results1.push({
        room,
        building: building.name,
        buildingId: building.id
      });
    }
  });
});
console.log('Results:', results1);
console.log('Expected: Should find RM300 in CICS building\n');

// Test 2: Search for EMB rooms
console.log('Test 2: Searching for "EMB"');
const results2 = [];
campus.buildings.forEach(building => {
  building.rooms.forEach(room => {
    if (room.toLowerCase().includes('emb')) {
      results2.push({
        room,
        building: building.name,
        buildingId: building.id
      });
    }
  });
});
console.log('Results:', results2);
console.log('Expected: Should find EMB rooms in Engineering building\n');

// Test 3: List all buildings with rooms
console.log('Test 3: Buildings with rooms');
campus.buildings.forEach(building => {
  if (building.rooms.length > 0) {
    console.log(`${building.name}: ${building.rooms.length} rooms`);
    console.log(`  Rooms: ${building.rooms.join(', ')}`);
  }
});
