"use client";
import React from 'react';
import { clsx } from 'clsx';

export interface FloatingActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'bottom-center' | 'right-center' | 'left-center';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'accent';
  className?: string;
  disabled?: boolean;
  ariaLabel?: string;
  tooltip?: string;
}

const positionClasses = {
  'bottom-right': 'bottom-6 right-6',
  'bottom-left': 'bottom-6 left-6',
  'top-right': 'top-6 right-6',
  'top-left': 'top-6 left-6',
  'bottom-center': 'bottom-6 left-1/2 transform -translate-x-1/2',
  'right-center': 'right-6 top-1/2 transform -translate-y-1/2',
  'left-center': 'left-6 top-1/2 transform -translate-y-1/2',
};

const sizeClasses = {
  sm: 'w-12 h-12 p-3',
  md: 'w-14 h-14 p-3.5',
  lg: 'w-16 h-16 p-4',
};

const variantClasses = {
  primary: 'bg-msu-maroon hover:bg-msu-maroon-dark text-white shadow-lg shadow-msu-maroon/30',
  secondary: 'bg-msu-gold hover:bg-msu-gold-dark text-msu-maroon shadow-lg shadow-msu-gold/30',
  accent: 'bg-white hover:bg-gray-50 text-msu-maroon shadow-lg shadow-black/20 border border-gray-200',
};

export default function FloatingActionButton({
  children,
  onClick,
  position = 'bottom-right',
  size = 'md',
  variant = 'primary',
  className,
  disabled = false,
  ariaLabel,
  tooltip,
}: FloatingActionButtonProps) {
  return (
    <div className="relative">
      <button
        onClick={onClick}
        disabled={disabled}
        aria-label={ariaLabel}
        className={clsx(
          'fab',
          'fixed z-50 rounded-full',
          'flex items-center justify-center',
          'transition-all duration-300 ease-out',
          'focus:outline-none focus:ring-4 focus:ring-msu-maroon/20',
          'active:scale-95',
          positionClasses[position],
          sizeClasses[size],
          variantClasses[variant],
          disabled && 'opacity-50 cursor-not-allowed',
          !disabled && 'hover:scale-110 hover:shadow-xl',
          className
        )}
      >
        {children}
      </button>
      
      {tooltip && (
        <div className={clsx(
          'absolute z-60 px-3 py-2 text-sm font-medium text-white',
          'bg-gray-900 rounded-lg shadow-lg',
          'opacity-0 invisible transition-all duration-200',
          'hover:opacity-100 hover:visible',
          'whitespace-nowrap',
          position.includes('right') ? 'right-full mr-3' : 'left-full ml-3',
          position.includes('bottom') ? 'bottom-0' : 'top-0'
        )}>
          {tooltip}
          <div className={clsx(
            'absolute w-2 h-2 bg-gray-900 transform rotate-45',
            position.includes('right') ? 'right-0 translate-x-1' : 'left-0 -translate-x-1',
            'top-1/2 -translate-y-1/2'
          )} />
        </div>
      )}
    </div>
  );
}

// Specialized FAB variants for common use cases
export function SearchFAB({ onClick, className, ...props }: Omit<FloatingActionButtonProps, 'children'>) {
  return (
    <FloatingActionButton
      onClick={onClick}
      position="left-center"
      variant="accent"
      ariaLabel="Search rooms"
      tooltip="Search Rooms"
      className={className}
      {...props}
    >
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </FloatingActionButton>
  );
}

export function ChatFAB({ onClick, className, ...props }: Omit<FloatingActionButtonProps, 'children'>) {
  return (
    <FloatingActionButton
      onClick={onClick}
      position="bottom-right"
      variant="primary"
      ariaLabel="Open chatbot"
      tooltip="Academic Assistant"
      className={className}
      {...props}
    >
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    </FloatingActionButton>
  );
}

export function InfoFAB({ onClick, className, ...props }: Omit<FloatingActionButtonProps, 'children'>) {
  return (
    <FloatingActionButton
      onClick={onClick}
      position="top-right"
      variant="secondary"
      size="sm"
      ariaLabel="Campus information"
      tooltip="Campus Info"
      className={className}
      {...props}
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    </FloatingActionButton>
  );
}
