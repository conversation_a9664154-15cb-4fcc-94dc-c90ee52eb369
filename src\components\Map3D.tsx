"use client";
import { <PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber";
import { OrbitControls, Html, useGLTF, useProgress } from "@react-three/drei";
import * as THREE from "three";
import { campus, Building } from "@/data/campus";
import React, { useEffect, useRef, useCallback, Suspense, useState } from "react";
import type { OrbitControls as OrbitControlsImpl } from "three-stdlib";

export type Map3DProps = {
  onSelectBuilding?: (b: Building | null) => void;
  focusTarget?: THREE.Vector3 | null;
  // New: highlight selected building
  selectedBuildingId?: string | null;
  selected?: Building | null;
  // New: optional camera zoom override (orthographic)
  cameraZoom?: number;
  setCameraZoom: (zoom: number | undefined) => void;
};

function Ground() {
  return (
    <mesh rotation-x={-Math.PI / 2} receiveShadow>
      <planeGeometry args={[1000, 1000]} />
      <meshStandardMaterial color="#a7f3d0" />
    </mesh>
  );
}

// Simple GLB model component
function Model({ url }: { url: string }) {
  const gltf = useGLTF(url);

  useEffect(() => {
    gltf.scene.traverse((obj) => {
      if (obj instanceof THREE.Mesh) {
        obj.castShadow = true;
        obj.receiveShadow = true;
        if (!obj.name) obj.name = obj.parent?.name || "Mesh";
      }
    });
  }, [gltf.scene]);

  return <primitive object={gltf.scene} />;
}

// Match a clicked object's name (or any ancestor's) to a campus building
function findBuildingFromObject(obj: THREE.Object3D | null): Building | null {
  const norm = (s: string) => s.trim().toLowerCase();
  const tryMatch = (name: string): Building | null => {
    const n = norm(name);
    if (!n) return null;
    // exact name match
    let b = campus.buildings.find((bb) => norm(bb.id) === n);
    if (b) return b;
    // object name contains building name
    b = campus.buildings.find((bb) => n.includes(norm(bb.name)) || norm(bb.name).includes(n));
    return b || null;
  };
  let cur: THREE.Object3D | null = obj;
  while (cur) {
    if (cur.name) {
      const b = tryMatch(cur.name);
      if (b) return b;
    }
    cur = cur.parent;
  }
  return null;
}

// Types for userData flags used by highlight logic
type HighlightUserData = {
  __origMaterial?: THREE.Material | THREE.Material[];
  __origEmissive?: THREE.Color;
  __origEmissiveIntensity?: number;
  __origColor?: THREE.Color;
} & Record<string, unknown>;

// Toggle highlight on a mesh or group of meshes without affecting other meshes that share materials
function setHighlight(root: THREE.Object3D, enabled: boolean) {
  root.traverse((o) => {
    if (!(o instanceof THREE.Mesh)) return;
    const mesh = o as THREE.Mesh;
    const ud = mesh.userData as HighlightUserData;

    const ensureUniqueMaterial = () => {
      if (!ud.__origMaterial) {
        // Save original material reference(s)
        ud.__origMaterial = mesh.material;
        // Detach by cloning so changes don't affect other meshes that share the same material
        if (Array.isArray(mesh.material)) {
          mesh.material = mesh.material.map((m) => (m ? m.clone() : m));
        } else if (mesh.material) {
          mesh.material = (mesh.material as THREE.Material).clone();
        }
      }
    };

    const applyHighlight = (m: THREE.Material) => {
      const mat = m as THREE.Material & {
        emissive?: THREE.Color;
        emissiveIntensity?: number;
        color?: THREE.Color;
        userData: HighlightUserData;
      };
      if (mat.emissive) {
        if (!mat.userData.__origEmissive) {
          mat.userData.__origEmissive = mat.emissive.clone();
          mat.userData.__origEmissiveIntensity =
            typeof mat.emissiveIntensity === "number" ? mat.emissiveIntensity : undefined;
        }
        mat.emissive.set("#fde047");
        if (typeof mat.emissiveIntensity === "number") mat.emissiveIntensity = 0.8;
      } else if (mat.color) {
        if (!mat.userData.__origColor) mat.userData.__origColor = mat.color.clone();
        mat.color.offsetHSL(0, 0, 0.2);
      }
    };

    const revertProps = (m: THREE.Material) => {
      const mat = m as THREE.Material & {
        emissive?: THREE.Color;
        emissiveIntensity?: number;
        color?: THREE.Color;
        userData: HighlightUserData;
      };
      const oe = mat.userData.__origEmissive;
      if (oe && mat.emissive) mat.emissive.copy(oe);
      const oi = mat.userData.__origEmissiveIntensity;
      if (typeof oi === "number" && typeof mat.emissiveIntensity === "number") mat.emissiveIntensity = oi;
      const oc = mat.userData.__origColor;
      if (oc && mat.color) mat.color.copy(oc);
    };

    if (enabled) {
      // Ensure we do not mutate shared materials
      ensureUniqueMaterial();
      const mm = mesh.material as THREE.Material | THREE.Material[] | undefined;
      if (!mm) return;
      if (Array.isArray(mm)) mm.forEach((m) => m && applyHighlight(m));
      else applyHighlight(mm);
    } else {
      // Restore original material(s) if we replaced them
      if (ud.__origMaterial) {
        const current = mesh.material as THREE.Material | THREE.Material[] | undefined;
        if (Array.isArray(current)) current.forEach((m) => m && m.dispose());
        else if (current) current.dispose();
        mesh.material = ud.__origMaterial;
        delete ud.__origMaterial;
      } else {
        // Fallback: attempt to revert any direct property changes if no original stored
        const mm = mesh.material as THREE.Material | THREE.Material[] | undefined;
        if (Array.isArray(mm)) mm.forEach((m) => m && revertProps(m));
        else if (mm) revertProps(mm);
      }
    }
  });
}

function LoadingOverlay() {
  const { progress } = useProgress();
  return (
    <Html center>
      <div className="px-3 py-2 text-sm rounded bg-black/70 text-white shadow">
        Loading 3D model… {Math.round(progress)}%
      </div>
    </Html>
  );
}

function FocusMarker({ target, label }: { target: THREE.Vector3; label?: string | null }) {
  const group = useRef<THREE.Group>(null);
  useFrame((state) => {
    if (group.current) {
      const t = state.clock.getElapsedTime();
      const s = 1 + Math.sin(t * 3) * 0.15;
      group.current.scale.setScalar(s);
    }
  });
  return (
    <group ref={group} position={[target.x, target.y + 0.2, target.z]}>
      <mesh>
        <sphereGeometry args={[0.6, 24, 24]} />
        <meshStandardMaterial color="#f59e0b" emissive="#f59e0b" emissiveIntensity={0.8} />
      </mesh>
      <mesh position={[0, -0.25, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <ringGeometry args={[0.6, 0.9, 32]} />
        <meshBasicMaterial color="#f59e0b" transparent opacity={0.9} />
      </mesh>
      {label ? (
        <Html position={[0, 1.2, 0]} center style={{ pointerEvents: "none" }}>
          <div className="px-2 py-1 text-xs rounded bg-black/70 text-white shadow">{label}</div>
        </Html>
      ) : null}
    </group>
  );
}

function CameraBoundaryControls({ focusTarget, targetZoom }: { focusTarget?: THREE.Vector3 | null; targetZoom?: number | null }) {
  const controls = useRef<OrbitControlsImpl | null>(null);
  const { camera } = useThree();

  useEffect(() => {
    if (!controls.current) return;
    // Touch: one-finger pan, two-finger dolly/pan
    // TOUCH.PAN = 1, TOUCH.DOLLY_PAN = 2
    controls.current.touches.ONE = 1;
    controls.current.touches.TWO = 2;

    // Mouse: left/right drag pan, middle dolly
    controls.current.mouseButtons.LEFT = THREE.MOUSE.PAN;
    controls.current.mouseButtons.MIDDLE = THREE.MOUSE.DOLLY;
    controls.current.mouseButtons.RIGHT = THREE.MOUSE.PAN;

    // Lock polar angle to 45° and disable rotate for a map-like experience
    controls.current.minPolarAngle = Math.PI / 4;
    controls.current.maxPolarAngle = Math.PI / 4;
    controls.current.enableRotate = false;
    controls.current.update();
  }, []);

  // Apply target zoom immediately when provided
  useEffect(() => {
    if (targetZoom == null) return;
    if (camera instanceof THREE.OrthographicCamera) {
      console.log(targetZoom)
      // Match min/maxZoom set on OrbitControls
      const clamped = Math.min(Math.max(targetZoom, 5), 20);
      if (camera.zoom !== clamped) {
        camera.zoom = clamped;
        camera.updateProjectionMatrix();
      }
    }
  }, [targetZoom, camera]);

  useFrame(({ camera }) => {
    // Smooth focus when provided
    if (focusTarget) {
      if (controls.current) {
        // Smoothly move target to focus position
        controls.current.target.lerp(new THREE.Vector3(focusTarget.x, 0, focusTarget.z), 0.12);
        // Lock camera rotation by maintaining consistent relative position to target
        const idealCameraPosition = new THREE.Vector3(
          controls.current.target.x,
          60,
          controls.current.target.z + 60
        );
        camera.position.lerp(idealCameraPosition, 0.12);

        // Ensure camera looks at the target
        camera.lookAt(controls.current.target);
        controls.current.update();
      }
    }

    // Constrain panning within campus bounds without changing view angle
    if (controls.current) {
      const t = controls.current.target;
      const beforeX = t.x;
      const beforeZ = t.z;

      // Clamp target to bounds
      t.x = Math.min(Math.max(t.x, campus.boundary.minX), campus.boundary.maxX);
      t.z = Math.min(Math.max(t.z, campus.boundary.minZ), campus.boundary.maxZ);
      t.y = 0; // keep target on ground

      // Translate camera by the same delta to prevent any unintended rotation/tilt
      const dx = t.x - beforeX;
      const dz = t.z - beforeZ;
      if (dx !== 0 || dz !== 0) {
        camera.position.x += dx;
        camera.position.z += dz;
        controls.current.update();
      }
    }
  });

  return (
    <OrbitControls
      ref={controls}
      enableRotate={false}
      enablePan
      enableZoom
      makeDefault
      screenSpacePanning={false}
      // For orthographic camera, control zoom via min/maxZoom
      minZoom={5}
      maxZoom={20}
    />
  );
}

function RaycastPicker({ getTargets, onPickBuilding, onIntersect }: { getTargets: () => THREE.Object3D[]; onPickBuilding?: (b: Building | null) => void; onIntersect?: (hit: THREE.Intersection | null) => void }) {
  const { camera, gl } = useThree();
  const raycasterRef = useRef(new THREE.Raycaster());
  const pointer = useRef(new THREE.Vector2());
  const gesture = useRef({ downX: 0, downY: 0, dragging: false });

  useEffect(() => {
    const onPointerDown = (e: PointerEvent) => {
      gesture.current.downX = e.clientX;
      gesture.current.downY = e.clientY;
      gesture.current.dragging = false;
    };
    const onPointerMove = (e: PointerEvent) => {
      if (gesture.current.dragging) return;
      const dx = Math.abs(e.clientX - gesture.current.downX);
      const dy = Math.abs(e.clientY - gesture.current.downY);
      if (dx + dy > 6) {
        gesture.current.dragging = true; // treat as pan, don't pick
      }
    };
    const onPointerUp = (e: PointerEvent) => {
      if (gesture.current.dragging) return; // ignore pans/drags

      const rect = gl.domElement.getBoundingClientRect();
      pointer.current.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
      pointer.current.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(pointer.current, camera);
      const targets = getTargets();
      const intersects = targets.length ? raycasterRef.current.intersectObjects(targets, true) : [];

      const hit = intersects[0] ?? null;
      onIntersect?.(hit ?? null);

      if (!hit) {
        onPickBuilding?.(null);
        return;
      }

      // If a building was clicked (when buildings are enabled), surface it
      let obj: THREE.Object3D | null = hit.object;
      let id: string | null = null;
      while (obj) {
        const ud = (obj as THREE.Object3D).userData as Record<string, unknown>;
        const maybe = typeof ud.name === "string" ? (ud.name as string) : undefined;
        if (maybe) { id = maybe; break; }
        obj = obj.parent;
      }

      if (id) {
        const b = campus.buildings.find((bb) => bb.id === id) || null;
        onPickBuilding?.(b);
      } else {
        onPickBuilding?.(null);
      }
    };

    const el = gl.domElement;
    el.addEventListener("pointerdown", onPointerDown);
    el.addEventListener("pointermove", onPointerMove);
    el.addEventListener("pointerup", onPointerUp);
    return () => {
      el.removeEventListener("pointerdown", onPointerDown);
      el.removeEventListener("pointermove", onPointerMove);
      el.removeEventListener("pointerup", onPointerUp);
    };
  }, [camera, gl, getTargets, onPickBuilding, onIntersect]);

  return null;
}

function WebGLContextGuard() {
  const { gl } = useThree();
  useEffect(() => {
    const canvas = gl.domElement;
    const onLost = (e: Event) => {
      e.preventDefault();
      console.warn("WebGL context lost. Attempting to restore...");
    };
    const onRestored = () => {
      console.info("WebGL context restored.");
    };
    canvas.addEventListener("webglcontextlost", onLost as EventListener, false);
    canvas.addEventListener("webglcontextrestored", onRestored as EventListener, false);
    return () => {
      canvas.removeEventListener("webglcontextlost", onLost as EventListener);
      canvas.removeEventListener("webglcontextrestored", onRestored as EventListener);
    };
  }, [gl]);
  return null;
}

export default function Map3D({ focusTarget, onSelectBuilding, selected, selectedBuildingId, cameraZoom, setCameraZoom }: Map3DProps) {
  // Reference to the GLTF model root for picking
  const modelRef = useRef<THREE.Object3D | null>(null);
  const [selectedObj, setSelectedObj] = useState<THREE.Object3D | null>(null);

  // For this test, only pick the model (disable campus buildings)
  const getTargets = useCallback(() => (modelRef.current ? [modelRef.current] : []), []);

  useEffect(() => {
    if (!selected) {
      if (selectedObj) setHighlight(selectedObj, false);
      setSelectedObj(null);
      onSelectBuilding?.(null);
      return;
    }
  }, [selected, onSelectBuilding, selectedObj]);

  // When a building id is provided externally (e.g., search select), highlight its object in the model
  useEffect(() => {
    // If the model isn't ready yet, or id cleared, unhighlight and bail
    if (!modelRef.current) return;

    if (!selectedBuildingId) {
      if (selectedObj) setHighlight(selectedObj, false);
      setSelectedObj(null);
      setCameraZoom(undefined);
      return;
    }

    let found: THREE.Object3D | null = null;
    modelRef.current.traverse((o) => {
      if (found) return;
      if (o.name && o.name === selectedBuildingId) {
        found = o;
      }
    });

    if (found) {
      if (selectedObj && selectedObj !== found) setHighlight(selectedObj, false);
      setHighlight(found, true);
      setSelectedObj(found);    } else {
      if (selectedObj) setHighlight(selectedObj, false);
      setSelectedObj(null);
    }
  }, [selectedBuildingId, selectedObj, setCameraZoom]);

  return (
    <div className="w-full h-full overflow-hidden">
      <Canvas
        shadows
        orthographic
        dpr={[1, 1]}
        gl={{
          antialias: true,
          alpha: false,
          preserveDrawingBuffer: true
        }}
        camera={{ position: [0, 60, 60], zoom: 10, near: 0.1, far: 2000 }}
        style={{ width: "100%", height: "100%", display: "block" }}
      >
        <WebGLContextGuard />
        <ambientLight intensity={0.7} />
        <directionalLight
          position={[50, 80, 40]}
          intensity={1.0}
          // castShadow
          // shadow-mapSize={[2048, 2048]}
          // shadow-camera-far={200}
          // shadow-camera-left={-350}
          // shadow-camera-right={350}
          // shadow-camera-top={350}
          // shadow-camera-bottom={-500}
        />
        <hemisphereLight args={["#87CEEB", "#98FB98", 0.3]} />
        <group>
          <Ground />
          <Suspense fallback={<LoadingOverlay />}>
            <group ref={modelRef}>
              <Model url="/scene.glb" />
            </group>          </Suspense>
          {focusTarget ? <FocusMarker target={focusTarget} /> : null}
          <BuildingLabels />
        </group>
        <CameraBoundaryControls focusTarget={focusTarget ?? null} targetZoom={cameraZoom ?? null} />
        <RaycastPicker
          getTargets={getTargets}
          onIntersect={(hit) => {
            // Clear selection on empty space
            if (!hit) {
              if (selectedObj) setHighlight(selectedObj, false);
              setSelectedObj(null);
              onSelectBuilding?.(null);
              return;
            }
            console.log(`position: {x: ${hit.point.x.toFixed(0)}, y: ${hit.point.y.toFixed(0)}, z: ${hit.point.z.toFixed(0)}},`);
            // Select clicked mesh and try to match a campus building by name
            const obj = hit.object as THREE.Object3D;
            const defaultMat = ['wall', 'rooftop'];
            console.log(obj.name)
            const isElemMat = obj instanceof THREE.Mesh && defaultMat.includes(obj.material.name)
            if (selectedObj && selectedObj !== obj) setHighlight(selectedObj, false);
            if (!isElemMat) setHighlight(obj, true);
            setSelectedObj(obj);
            const building = findBuildingFromObject(obj);
            if (building) {
              onSelectBuilding?.(building);

            } else {
              onSelectBuilding?.(null);
              // keep highlight even if no campus match so user sees the selection
            }
          }}
        />
      </Canvas>
    </div>
  );
}

// Optionally preload the model
useGLTF.preload?.("/scene.glb");

function BuildingLabels() {
  const { camera } = useThree();
  const [cameraDistance, setCameraDistance] = useState(0);

  const mainBldgs = campus.buildings.filter((b) => b.type === "main");

  useFrame(() => {
    // Calculate camera distance from target (approximate zoom level)
    // For orthographic camera, we can use camera.zoom or camera.position.y
    if (camera instanceof THREE.OrthographicCamera) {
      setCameraDistance(camera.zoom);
    } else {
      // For perspective camera, use distance from origin
      setCameraDistance(camera.position.distanceTo(new THREE.Vector3(0, 0, 0)));
    }
  });

  // Show labels when camera is zoomed in enough
  // Adjust these values based on your camera setup
  const minZoomForLabels = 5; // Show labels when zoom > 8
  const showLabels = cameraDistance > minZoomForLabels;

  if (!showLabels) return null;

  return (
    <>
      {mainBldgs.map((building) => (
        <group key={building.id} position={[building.position.x, building.position.y + 8, building.position.z]}>
          <Html
            center
            style={{
              pointerEvents: "none",
              userSelect: "none",
            }}
          >
            <div
              className="px-3 py-2 text-sm font-medium rounded-lg shadow-lg transition-all duration-300"
              style={{
                backgroundColor: building.color || "#ffffff",
                color: getContrastColor(building.color || "#ffffff"),
                border: "2px solid rgba(255, 255, 255, 0.3)",
                backdropFilter: "blur(4px)",
                maxWidth: "200px",
                textAlign: "center",
                fontSize: "12px",
                lineHeight: "1.2",
                opacity: 0.9,
              }}
            >
              {building.acronym.toUpperCase()}
            </div>
          </Html>
        </group>
      ))}
    </>
  );
}

// Helper function to determine contrasting text color
function getContrastColor(hexColor: string): string {
  // Remove # if present
  const hex = hexColor.replace('#', '');

  // Convert to RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return black for light colors, white for dark colors
  return luminance > 0.5 ? '#000000' : '#ffffff';
}
