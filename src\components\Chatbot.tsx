"use client";
import React, { useEffect, useRef, useState } from "react";
import { PaperAirplaneIcon, ChatBubbleLeftRightIcon } from "@heroicons/react/24/solid";

export default function Chatbot() {
  const [open, setOpen] = useState(false);
  const [messages, setMessages] = useState<{ role: "user" | "assistant"; content: string }[]>(
    [
      { role: "assistant", content: "Hi! I'm your MSU academic assistant. Ask me about programs, requirements, or departments." },
    ],
  );
  const [input, setInput] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (open) inputRef.current?.focus();
  }, [open]);

  async function send() {
    const q = input.trim();
    if (!q) return;
    setInput("");
    setMessages((m) => [...m, { role: "user", content: q }]);
    try {
      const res = await fetch("/api/chat", { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ question: q }) });
      const data = await res.json();
      setMessages((m) => [...m, { role: "assistant", content: data.answer || "Sorry, I couldn't find that." }]);
    } catch {
      setMessages((m) => [...m, { role: "assistant", content: "Network error." }]);
    }
  }

  return (
    <>
      {/* Enhanced Chatbot Button with MSU Colors */}
      <button
        className="fixed bottom-4 right-4 z-40 rounded-full bg-gradient-to-r from-msu-gold to-yellow-500 text-msu-maroon p-5 shadow-2xl hover:shadow-3xl hover:scale-110 focus:outline-none transition-all duration-300 border-2 border-msu-maroon/20 animate-pulse hover:animate-none"
        onClick={() => setOpen((o) => !o)}
        aria-label="Toggle chatbot"
      >
        <ChatBubbleLeftRightIcon className="h-7 w-7" />
        {/* Notification Badge */}
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-msu-maroon rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">!</span>
        </div>
      </button>

      {/* Enhanced Chat Window */}
      <div className={`fixed bottom-24 right-4 z-40 w-[24rem] max-w-[calc(100vw-2rem)] bg-white/95 backdrop-blur-md border-2 border-msu-gold/30 shadow-2xl rounded-2xl overflow-hidden transition-all duration-300 ${open ? 'translate-y-0 opacity-100' : 'translate-y-[140%] opacity-0'}`}>
        {/* Header with MSU Branding */}
        <div className="p-4 border-b bg-gradient-to-r from-msu-maroon via-msu-maroon-dark to-msu-maroon text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-msu-gold rounded-full flex items-center justify-center">
                <span className="text-msu-maroon font-bold text-sm">M</span>
              </div>
              <div>
                <div className="font-semibold text-sm">MSU Academic Assistant</div>
                <div className="text-xs text-white/80">Ask me anything about MSU!</div>
              </div>
            </div>
            <button
              onClick={() => setOpen(false)}
              className="text-white/80 hover:text-white transition-colors p-1"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Messages Area */}
        <div className="h-72 overflow-y-auto p-4 space-y-3 text-sm bg-gradient-to-b from-white/90 to-msu-gold/5">
          {messages.map((m, i) => (
            <div key={i} className={`flex ${m.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[85%] px-4 py-3 rounded-2xl ${
                m.role === 'user'
                  ? 'bg-gradient-to-r from-msu-maroon to-msu-maroon-dark text-white shadow-lg'
                  : 'bg-white/90 text-slate-900 shadow-md border border-msu-gold/20 backdrop-blur-sm'
              }`}>
                {m.content}
              </div>
            </div>
          ))}
        </div>

        {/* Input Area */}
        <div className="p-4 border-t border-msu-gold/20 bg-white/90 backdrop-blur-sm flex items-center gap-3">
          <input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && send()}
            placeholder="Ask about programs, requirements..."
            className="flex-1 border-2 border-msu-gold/30 rounded-xl px-4 py-3 text-sm outline-none focus:ring-2 focus:ring-msu-maroon/20 focus:border-msu-maroon transition-all duration-200 bg-white/80"
          />
          <button
            onClick={send}
            className="p-3 rounded-xl bg-gradient-to-r from-msu-gold to-yellow-500 text-msu-maroon hover:shadow-lg hover:scale-105 transition-all duration-200 border border-msu-maroon/20"
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </>
  );
}
